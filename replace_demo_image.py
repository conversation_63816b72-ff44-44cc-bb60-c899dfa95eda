#!/usr/bin/env python3
"""
FGSat Demo Image Replacement Tool

Allows you to replace a specific image and its corresponding label in the demo dataset
with another image from the original dataset.
"""

import os
import json
import shutil
import cv2
import numpy as np
from pathlib import Path
import matplotlib.pyplot as plt
import matplotlib.image as mpimg

class ImageReplacer:
    def __init__(self, demo_dir, source_dir):
        self.demo_dir = Path(demo_dir)
        self.source_dir = Path(source_dir)
        
        # Load dataset info to get class mapping
        self.dataset_info = self.load_dataset_info()
        self.spacecraft_classes = self.dataset_info.get('classes', {})
        
    def load_dataset_info(self):
        """Load dataset information from JSON file"""
        info_path = self.demo_dir / "dataset_info.json"
        if info_path.exists():
            with open(info_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                # Convert string keys to integers for class mapping
                if 'classes' in data and isinstance(data['classes'], dict):
                    classes = {}
                    for k, v in data['classes'].items():
                        try:
                            classes[int(k)] = v
                        except ValueError:
                            classes[k] = v
                    data['classes'] = classes
                return data
        return {}
    
    def parse_filename(self, filename):
        """Parse demo filename to extract spacecraft name, type, and index"""
        # Format: SpacecraftName_ImageType_Index.png
        stem = Path(filename).stem
        parts = stem.split('_')
        if len(parts) >= 3:
            spacecraft_name = parts[0]
            image_type = parts[1]
            index = int(parts[2])
            return spacecraft_name, image_type, index
        return None, None, None
    
    def find_spacecraft_folder(self, spacecraft_name):
        """Find the source folder for a spacecraft"""
        for folder in self.source_dir.iterdir():
            if folder.is_dir() and not folder.name.startswith("ZZ-"):
                clean_name = self.clean_spacecraft_name(folder.name)
                if clean_name == spacecraft_name:
                    return folder
        return None
    
    def clean_spacecraft_name(self, folder_name):
        """Clean spacecraft name from folder name"""
        if '-' in folder_name:
            name = folder_name.split('-', 1)[1]
        else:
            name = folder_name
        name = name.replace('_', '-')
        name = name.strip()
        return name
    
    def get_available_images(self, spacecraft_folder, image_type):
        """Get all available images for a spacecraft and image type"""
        if image_type.lower() == "rgb":
            subfolder = spacecraft_folder / "1-RGB-700"
        elif image_type.lower() == "grayscale":
            subfolder = spacecraft_folder / "2-灰度-700"
        else:
            return []
        
        if not subfolder.exists():
            return []
        
        # Get all camera images
        images = []
        for img_file in subfolder.glob("*.camera.png"):
            images.append(img_file)
        
        # Sort by step number
        images.sort(key=lambda x: int(x.stem.split('.')[0].replace('step', '')))
        return images
    
    def preview_image(self, image_path, title=""):
        """Preview an image"""
        try:
            image = mpimg.imread(str(image_path))
            
            if image is not None:
                # Handle format conversion
                if image.dtype != np.uint8:
                    image = (image * 255).astype(np.uint8)
                
                # Handle RGBA to RGB conversion
                if len(image.shape) == 3 and image.shape[2] == 4:
                    image = image[:, :, :3]
                
                plt.figure(figsize=(10, 6))
                plt.imshow(image)
                plt.title(f"{title}\n{image_path.name}", fontsize=12)
                plt.axis('off')
                plt.tight_layout()
                plt.show(block=False)
                plt.pause(0.1)
                
                return True
        except Exception as e:
            print(f"Error previewing image: {e}")
        return False
    
    def unity_to_yolo_bbox(self, origin, dimension, img_width=1920, img_height=1080):
        """Convert Unity Solo bounding box to YOLO format"""
        x_min, y_min = origin
        width, height = dimension
        
        # Calculate center point
        center_x = x_min + width / 2
        center_y = y_min + height / 2
        
        # Normalize coordinates
        center_x_norm = center_x / img_width
        center_y_norm = center_y / img_height
        width_norm = width / img_width
        height_norm = height / img_height
        
        return [center_x_norm, center_y_norm, width_norm, height_norm]
    
    def convert_json_to_yolo(self, json_path, spacecraft_name):
        """Convert Unity Solo JSON annotation to YOLO format"""
        try:
            with open(json_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            yolo_lines = []
            captures = data.get("captures", [])
            
            if captures and "annotations" in captures[0]:
                for annotation in captures[0]["annotations"]:
                    if annotation.get("@type") == "type.unity.com/unity.solo.BoundingBox2DAnnotation":
                        values = annotation.get("values", [])
                        
                        for value in values:
                            label_name = value.get("labelName")
                            origin = value.get("origin")
                            dimension = value.get("dimension")
                            
                            if label_name and origin and dimension:
                                # Find class ID for this spacecraft
                                class_id = None
                                for cid, cname in self.spacecraft_classes.items():
                                    if cname == spacecraft_name:
                                        class_id = cid
                                        break
                                
                                if class_id is not None:
                                    # Convert bounding box to YOLO format
                                    yolo_bbox = self.unity_to_yolo_bbox(origin, dimension)
                                    
                                    # Format as YOLO line
                                    line = f"{class_id} {' '.join(f'{coord:.6f}' for coord in yolo_bbox)}"
                                    yolo_lines.append(line)
            
            return '\n'.join(yolo_lines) if yolo_lines else ""
            
        except Exception as e:
            print(f"Error converting JSON to YOLO: {e}")
            return None
    
    def replace_image(self, target_filename):
        """Replace a specific image in the demo dataset"""
        print(f"🔄 Replacing image: {target_filename}")
        
        # Parse target filename
        spacecraft_name, image_type, index = self.parse_filename(target_filename)
        if not spacecraft_name:
            print("❌ Invalid filename format")
            return False
        
        print(f"   Spacecraft: {spacecraft_name}")
        print(f"   Type: {image_type}")
        print(f"   Index: {index}")
        
        # Find source folder
        spacecraft_folder = self.find_spacecraft_folder(spacecraft_name)
        if not spacecraft_folder:
            print(f"❌ Source folder not found for {spacecraft_name}")
            return False
        
        # Get available images
        available_images = self.get_available_images(spacecraft_folder, image_type)
        if not available_images:
            print(f"❌ No images found for {spacecraft_name} {image_type}")
            return False
        
        print(f"   Found {len(available_images)} available images")
        
        # Let user select replacement image
        selected_image = self.select_replacement_image(available_images, spacecraft_name, image_type)
        if not selected_image:
            print("❌ No replacement image selected")
            return False
        
        # Perform replacement
        return self.perform_replacement(target_filename, selected_image, spacecraft_name, image_type, index)
    
    def select_replacement_image(self, available_images, spacecraft_name, image_type):
        """Let user select a replacement image"""
        print(f"\n📋 Select replacement image for {spacecraft_name} {image_type}")
        print("   Available images:")
        
        for i, img_path in enumerate(available_images[:20]):  # Show first 20
            step_num = img_path.stem.split('.')[0].replace('step', '')
            print(f"      {i+1:2d}: {img_path.name} (step {step_num})")
        
        if len(available_images) > 20:
            print(f"      ... and {len(available_images) - 20} more")
        
        while True:
            try:
                choice = input(f"\n   Enter image number (1-{min(20, len(available_images))}) or 'q' to quit: ").strip()
                
                if choice.lower() == 'q':
                    return None
                
                idx = int(choice) - 1
                if 0 <= idx < min(20, len(available_images)):
                    selected_image = available_images[idx]
                    
                    # Preview the selected image
                    print(f"   👀 Previewing: {selected_image.name}")
                    self.preview_image(selected_image, f"{spacecraft_name} {image_type} - Replacement Candidate")
                    
                    confirm = input("   Confirm this selection? (y/n): ").strip().lower()
                    plt.close()  # Close preview
                    
                    if confirm == 'y':
                        return selected_image
                    else:
                        continue
                else:
                    print("   ❗ Invalid selection")
                    
            except ValueError:
                print("   ❗ Please enter a valid number")
            except KeyboardInterrupt:
                print("\n   Operation cancelled")
                return None
    
    def perform_replacement(self, target_filename, source_image, spacecraft_name, image_type, index):
        """Perform the actual replacement"""
        try:
            # Determine target paths
            if image_type.lower() == "rgb":
                target_img_dir = self.demo_dir / "RGB"
                target_label_dir = self.demo_dir / "RGB_Labels"
            else:
                target_img_dir = self.demo_dir / "Grayscale"
                target_label_dir = self.demo_dir / "Grayscale_Labels"
            
            target_img_path = target_img_dir / target_filename
            target_label_path = target_label_dir / (Path(target_filename).stem + ".txt")
            
            # Find corresponding JSON file
            json_path = source_image.with_suffix('.json').with_name(
                source_image.stem.replace('.camera', '') + '.frame_data.json'
            )
            
            # Copy image
            if image_type.lower() == "grayscale" and "灰度" not in str(source_image):
                # Convert RGB to grayscale
                image = cv2.imread(str(source_image))
                if image is not None:
                    gray_image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
                    cv2.imwrite(str(target_img_path), gray_image)
                    print(f"   ✅ Converted and saved image: {target_img_path}")
                else:
                    print(f"   ❌ Failed to load source image")
                    return False
            else:
                # Copy directly
                shutil.copy2(source_image, target_img_path)
                print(f"   ✅ Copied image: {target_img_path}")
            
            # Convert and save label
            if json_path.exists():
                yolo_content = self.convert_json_to_yolo(json_path, spacecraft_name)
                if yolo_content is not None:
                    with open(target_label_path, 'w', encoding='utf-8') as f:
                        f.write(yolo_content)
                    print(f"   ✅ Updated label: {target_label_path}")
                else:
                    print(f"   ⚠️ Failed to convert label, creating empty file")
                    target_label_path.touch()
            else:
                print(f"   ⚠️ JSON file not found: {json_path}")
                target_label_path.touch()
            
            print(f"   🎉 Successfully replaced {target_filename}")
            return True
            
        except Exception as e:
            print(f"   ❌ Error during replacement: {e}")
            return False

def main():
    """Main function"""
    print("🔄 FGSat Demo Image Replacement Tool")
    print("=" * 50)
    
    # Configuration
    demo_dir = r"G:\ICCSSE\数据集\FGSat_Demo"
    source_dir = r"G:\ICCSSE\数据集\AA-最终版自己的数据库"
    
    replacer = ImageReplacer(demo_dir, source_dir)
    
    # Get target filename from user
    target_filename = input("Enter the filename to replace (e.g., XMM-Newton_grayscale_009.png): ").strip()
    
    if not target_filename:
        print("❌ No filename provided")
        return
    
    # Perform replacement
    success = replacer.replace_image(target_filename)
    
    if success:
        print("\n🎉 Image replacement completed successfully!")
    else:
        print("\n❌ Image replacement failed!")

if __name__ == "__main__":
    main()
