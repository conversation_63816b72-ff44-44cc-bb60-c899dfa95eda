#!/usr/bin/env python3
"""
Simple test script to diagnose image loading issues
"""

import cv2
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.image as mpimg
from pathlib import Path

def test_image_loading():
    """Test different methods of loading the problematic images"""
    
    # Test files that were reported as problematic
    test_files = [
        "G:/ICCSSE/数据集/FGSat_Demo/RGB/SWAS_rgb_007.png",
        "G:/ICCSSE/数据集/FGSat_Demo/RGB/Cluster_rgb_005.png",
        "G:/ICCSSE/数据集/FGSat_Demo/RGB/GPM_rgb_007.png"
    ]
    
    for file_path in test_files:
        print(f"\n🔍 Testing: {file_path}")
        path_obj = Path(file_path)
        
        # Check if file exists
        if not path_obj.exists():
            print(f"   ❌ File does not exist")
            continue
        
        print(f"   ✅ File exists, size: {path_obj.stat().st_size} bytes")
        
        # Method 1: cv2.imread
        try:
            img_cv2 = cv2.imread(file_path)
            if img_cv2 is not None:
                print(f"   ✅ cv2.imread: SUCCESS, shape: {img_cv2.shape}")
            else:
                print(f"   ❌ cv2.imread: FAILED")
        except Exception as e:
            print(f"   ❌ cv2.imread: ERROR - {e}")
        
        # Method 2: cv2.imread with resolved path
        try:
            resolved_path = str(path_obj.resolve())
            img_cv2_resolved = cv2.imread(resolved_path)
            if img_cv2_resolved is not None:
                print(f"   ✅ cv2.imread (resolved): SUCCESS, shape: {img_cv2_resolved.shape}")
            else:
                print(f"   ❌ cv2.imread (resolved): FAILED")
        except Exception as e:
            print(f"   ❌ cv2.imread (resolved): ERROR - {e}")
        
        # Method 3: matplotlib.image
        try:
            img_mpl = mpimg.imread(file_path)
            if img_mpl is not None:
                print(f"   ✅ matplotlib: SUCCESS, shape: {img_mpl.shape}, dtype: {img_mpl.dtype}")
            else:
                print(f"   ❌ matplotlib: FAILED")
        except Exception as e:
            print(f"   ❌ matplotlib: ERROR - {e}")
        
        # Method 4: PIL
        try:
            from PIL import Image
            img_pil = Image.open(file_path)
            img_array = np.array(img_pil)
            print(f"   ✅ PIL: SUCCESS, shape: {img_array.shape}, dtype: {img_array.dtype}")
        except Exception as e:
            print(f"   ❌ PIL: ERROR - {e}")

if __name__ == "__main__":
    print("🧪 Image Loading Diagnostic Test")
    print("=" * 50)
    test_image_loading()