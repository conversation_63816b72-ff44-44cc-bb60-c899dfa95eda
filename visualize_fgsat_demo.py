#!/usr/bin/env python3
"""
FGSat Demo Dataset Visualization Tool

Visualizes the FGSat demo dataset to verify:
- Image and label file correspondence
- Bounding box accuracy and positioning
- Class label correctness

Features:
- Interactive browsing of dataset samples
- Batch visualization with grid layout
- Statistics and validation reports
- Support for both RGB and Grayscale data
"""

import os
import json
import cv2
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from pathlib import Path
import random
from collections import defaultdict, Counter
import argparse

# Set Chinese font support
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class FGSatDemoVisualizer:
    def __init__(self, dataset_path):
        self.dataset_path = Path(dataset_path)
        self.dataset_info = self.load_dataset_info()
        self.class_names = self.dataset_info.get('classes', {})
        self.colors = self.generate_colors(len(self.class_names))
        
        # Dataset directories
        self.rgb_images_dir = self.dataset_path / "RGB"
        self.rgb_labels_dir = self.dataset_path / "RGB_Labels"
        self.gray_images_dir = self.dataset_path / "Grayscale"
        self.gray_labels_dir = self.dataset_path / "Grayscale_Labels"
        
        print(f"📁 Dataset loaded: {self.dataset_path}")
        print(f"🛰️  Classes: {len(self.class_names)}")
        
    def load_dataset_info(self):
        """Load dataset information from JSON file"""
        info_path = self.dataset_path / "dataset_info.json"
        if info_path.exists():
            with open(info_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                # Convert string keys to integers for class mapping
                if 'classes' in data and isinstance(data['classes'], dict):
                    classes = {}
                    for k, v in data['classes'].items():
                        try:
                            classes[int(k)] = v
                        except ValueError:
                            classes[k] = v
                    data['classes'] = classes
                return data
        else:
            print(f"⚠️ Dataset info file not found: {info_path}")
            return {}
    
    def generate_colors(self, num_classes):
        """Generate distinct colors for each class"""
        colors = []
        for i in range(num_classes):
            # Generate evenly distributed colors in HSV space
            hue = (i * 360 / num_classes) % 360
            saturation = 0.8 + (i % 3) * 0.1  # 0.8, 0.9, 1.0
            value = 0.7 + (i % 2) * 0.2       # 0.7, 0.9
            
            # Convert HSV to RGB
            hsv = np.array([[[hue/2, saturation*255, value*255]]], dtype=np.uint8)
            rgb = cv2.cvtColor(hsv, cv2.COLOR_HSV2RGB)[0][0]
            colors.append((int(rgb[0])/255.0, int(rgb[1])/255.0, int(rgb[2])/255.0))
        
        return colors
    
    def yolo_to_bbox(self, yolo_coords, img_width, img_height):
        """Convert YOLO coordinates to pixel coordinates"""
        center_x, center_y, width, height = yolo_coords
        
        # Denormalize
        center_x *= img_width
        center_y *= img_height
        width *= img_width
        height *= img_height
        
        # Convert to top-left corner coordinates
        x_min = center_x - width / 2
        y_min = center_y - height / 2
        
        return [x_min, y_min, width, height]
    
    def load_yolo_labels(self, label_path):
        """Load YOLO format labels from file"""
        labels = []
        
        if not label_path.exists():
            return labels
        
        try:
            with open(label_path, 'r', encoding='utf-8') as f:
                lines = f.read().strip().split('\n')
                
            for line in lines:
                if line.strip():
                    parts = line.strip().split()
                    if len(parts) >= 5:
                        class_id = int(parts[0])
                        coords = [float(x) for x in parts[1:5]]
                        labels.append((class_id, coords))
        
        except Exception as e:
            print(f"❌ Error loading labels from {label_path}: {e}")
        
        return labels
    
    def visualize_single_sample(self, image_path, label_path, title="", save_path=None):
        """Visualize a single image with its bounding boxes"""
        # Load image with better error handling
        if not image_path.exists():
            print(f"❌ Image file does not exist: {image_path}")
            return None
        
        if image_path.suffix.lower() in ['.png', '.jpg', '.jpeg']:
            try:
                # Use matplotlib for reliable loading (避免OpenCV兼容性问题)
                import matplotlib.image as mpimg
                image = mpimg.imread(str(image_path))
                
                if image is not None:
                    # Handle different image formats
                    if image.dtype != np.uint8:
                        # Convert from float32 [0,1] to uint8 [0,255]
                        image = (image * 255).astype(np.uint8)
                    
                    # Handle RGBA (4-channel) images
                    if len(image.shape) == 3 and image.shape[2] == 4:
                        # Convert RGBA to RGB by removing alpha channel
                        image = image[:, :, :3]
                        print(f"   ℹ️  Converted RGBA to RGB: {image_path.name}")
                    elif len(image.shape) == 3 and image.shape[2] == 3:
                        # Already RGB, keep as is
                        pass
                    else:
                        print(f"❌ Unexpected image format: {image.shape}")
                        return None
                else:
                    print(f"❌ Failed to load image: {image_path}")
                    return None
                    
            except Exception as e:
                print(f"❌ Error loading image {image_path}: {e}")
                return None
        else:
            print(f"❌ Unsupported image format: {image_path}")
            return None
        
        img_height, img_width = image.shape[:2]
        
        # Load labels
        labels = self.load_yolo_labels(label_path)
        
        # Create plot
        fig, ax = plt.subplots(1, 1, figsize=(12, 8))
        ax.imshow(image)
        ax.set_title(f"{title}\nImage: {image_path.name} | Labels: {len(labels)}", fontsize=14)
        ax.axis('off')
        
        # Draw bounding boxes
        for class_id, yolo_coords in labels:
            # Convert YOLO to pixel coordinates
            x_min, y_min, width, height = self.yolo_to_bbox(yolo_coords, img_width, img_height)
            
            # Get class name and color
            class_name = self.class_names.get(class_id, f"Class_{class_id}")
            color = self.colors[class_id % len(self.colors)]
            
            # Create rectangle
            rect = patches.Rectangle((x_min, y_min), width, height, 
                                   linewidth=2, edgecolor=color, facecolor='none')
            ax.add_patch(rect)
            
            # Add class label
            ax.text(x_min, y_min-10, f"{class_name} ({class_id})", 
                   bbox=dict(boxstyle="round,pad=0.3", facecolor=color, alpha=0.7),
                   fontsize=10, color='white', weight='bold')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=150, bbox_inches='tight')
            print(f"💾 Saved visualization: {save_path}")
        
        plt.show()
        return fig
    
    def get_sample_files(self, data_type="rgb", num_samples=None):
        """Get sample image and label file pairs"""
        if data_type.lower() == "rgb":
            images_dir = self.rgb_images_dir
            labels_dir = self.rgb_labels_dir
        elif data_type.lower() == "grayscale":
            images_dir = self.gray_images_dir
            labels_dir = self.gray_labels_dir
        else:
            raise ValueError("data_type must be 'rgb' or 'grayscale'")
        
        if not images_dir.exists() or not labels_dir.exists():
            print(f"❌ Directory not found: {images_dir} or {labels_dir}")
            return []
        
        # Get all image files
        image_files = list(images_dir.glob("*.png"))
        image_files.sort()
        
        # Find corresponding label files
        sample_pairs = []
        for img_path in image_files:
            label_path = labels_dir / (img_path.stem + ".txt")
            if label_path.exists():
                sample_pairs.append((img_path, label_path))
            else:
                print(f"⚠️ Label file not found: {label_path}")
        
        if num_samples and len(sample_pairs) > num_samples:
            # Randomly sample
            sample_pairs = random.sample(sample_pairs, num_samples)
        
        return sample_pairs
    
    def visualize_grid(self, data_type="rgb", num_samples=9, save_path=None):
        """Visualize multiple samples in a grid layout"""
        sample_pairs = self.get_sample_files(data_type, num_samples)
        
        if not sample_pairs:
            print(f"❌ No valid samples found for {data_type} data")
            return None
        
        # Calculate grid size
        grid_size = int(np.ceil(np.sqrt(len(sample_pairs))))
        
        # Create plot
        fig, axes = plt.subplots(grid_size, grid_size, figsize=(16, 16))
        fig.suptitle(f"FGSat Demo Dataset - {data_type.upper()} Samples", fontsize=16, y=0.98)
        
        # Flatten axes for easier indexing
        axes = axes.flatten() if grid_size > 1 else [axes]
        
        for idx, (img_path, label_path) in enumerate(sample_pairs):
            if idx >= len(axes):
                break
                
            ax = axes[idx]
            
            # Load and display image with matplotlib (避免OpenCV问题)
            image = None
            if img_path.exists():
                try:
                    import matplotlib.image as mpimg
                    image = mpimg.imread(str(img_path))
                    
                    if image is not None:
                        # Handle format conversion
                        if image.dtype != np.uint8:
                            image = (image * 255).astype(np.uint8)
                        
                        # Handle RGBA to RGB conversion
                        if len(image.shape) == 3 and image.shape[2] == 4:
                            image = image[:, :, :3]  # Remove alpha channel
                            
                except Exception as e:
                    print(f"⚠️ Error loading {img_path.name}: {e}")
                    image = None
            
            if image is not None:
                img_height, img_width = image.shape[:2]
                
                ax.imshow(image)
                
                # Load and draw labels
                labels = self.load_yolo_labels(label_path)
                
                for class_id, yolo_coords in labels:
                    # Convert to pixel coordinates
                    x_min, y_min, width, height = self.yolo_to_bbox(yolo_coords, img_width, img_height)
                    
                    # Get class info
                    class_name = self.class_names.get(class_id, f"Class_{class_id}")
                    color = self.colors[class_id % len(self.colors)]
                    
                    # Draw bounding box
                    rect = patches.Rectangle((x_min, y_min), width, height,
                                           linewidth=1.5, edgecolor=color, facecolor='none')
                    ax.add_patch(rect)
                    
                    # Add small label
                    ax.text(x_min, y_min-5, class_name, fontsize=8, 
                           bbox=dict(boxstyle="round,pad=0.2", facecolor=color, alpha=0.7),
                           color='white', weight='bold')
                
                # Set title
                spacecraft_name = img_path.stem.split('_')[0]
                ax.set_title(f"{spacecraft_name}", fontsize=10)
            
            ax.axis('off')
        
        # Hide unused subplots
        for idx in range(len(sample_pairs), len(axes)):
            axes[idx].axis('off')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=200, bbox_inches='tight')
            print(f"💾 Saved grid visualization: {save_path}")
        
        plt.show()
        return fig
    
    def validate_dataset(self):
        """Validate the dataset and print statistics"""
        print("\n🔍 Dataset Validation Report")
        print("=" * 50)
        
        # Check RGB data
        rgb_stats = self.validate_data_type("rgb")
        print(f"\n📊 RGB Dataset:")
        self.print_validation_stats(rgb_stats)
        
        # Check Grayscale data
        gray_stats = self.validate_data_type("grayscale") 
        print(f"\n📊 Grayscale Dataset:")
        self.print_validation_stats(gray_stats)
        
        # Overall statistics
        total_images = rgb_stats['images_found'] + gray_stats['images_found']
        total_labels = rgb_stats['labels_found'] + gray_stats['labels_found']
        total_matched = rgb_stats['matched_pairs'] + gray_stats['matched_pairs']
        
        print(f"\n📈 Overall Statistics:")
        print(f"   Total Images: {total_images}")
        print(f"   Total Labels: {total_labels}")
        print(f"   Matched Pairs: {total_matched}")
        print(f"   Match Rate: {total_matched/total_images*100:.1f}%" if total_images > 0 else "   Match Rate: N/A")
        
        # Class distribution
        self.analyze_class_distribution()
        
        return {
            'rgb': rgb_stats,
            'grayscale': gray_stats,
            'total_images': total_images,
            'total_labels': total_labels,
            'total_matched': total_matched
        }
    
    def validate_data_type(self, data_type):
        """Validate a specific data type (rgb or grayscale)"""
        if data_type.lower() == "rgb":
            images_dir = self.rgb_images_dir
            labels_dir = self.rgb_labels_dir
        else:
            images_dir = self.gray_images_dir
            labels_dir = self.gray_labels_dir
        
        stats = {
            'images_found': 0,
            'labels_found': 0,
            'matched_pairs': 0,
            'missing_labels': [],
            'missing_images': [],
            'bbox_errors': [],
            'class_counts': Counter()
        }
        
        if not images_dir.exists() or not labels_dir.exists():
            return stats
        
        # Check images
        image_files = list(images_dir.glob("*.png"))
        stats['images_found'] = len(image_files)
        
        # Check labels
        label_files = list(labels_dir.glob("*.txt"))
        stats['labels_found'] = len(label_files)
        
        # Check correspondence
        for img_path in image_files:
            label_path = labels_dir / (img_path.stem + ".txt")
            
            if label_path.exists():
                stats['matched_pairs'] += 1
                
                # Validate label content
                labels = self.load_yolo_labels(label_path)
                for class_id, coords in labels:
                    stats['class_counts'][class_id] += 1
                    
                    # Check coordinate validity
                    if not all(0 <= coord <= 1 for coord in coords):
                        stats['bbox_errors'].append(str(label_path))
            else:
                stats['missing_labels'].append(str(img_path))
        
        # Check for orphaned labels
        for label_path in label_files:
            img_path = images_dir / (label_path.stem + ".png")
            if not img_path.exists():
                stats['missing_images'].append(str(label_path))
        
        return stats
    
    def print_validation_stats(self, stats):
        """Print validation statistics"""
        print(f"   Images Found: {stats['images_found']}")
        print(f"   Labels Found: {stats['labels_found']}")
        print(f"   Matched Pairs: {stats['matched_pairs']}")
        
        if stats['missing_labels']:
            print(f"   ⚠️ Missing Labels: {len(stats['missing_labels'])}")
        
        if stats['missing_images']:
            print(f"   ⚠️ Missing Images: {len(stats['missing_images'])}")
        
        if stats['bbox_errors']:
            print(f"   ❌ Bbox Errors: {len(stats['bbox_errors'])}")
        
        match_rate = stats['matched_pairs'] / stats['images_found'] * 100 if stats['images_found'] > 0 else 0
        print(f"   ✅ Match Rate: {match_rate:.1f}%")
    
    def analyze_class_distribution(self):
        """Analyze class distribution across the dataset"""
        print(f"\n📊 Class Distribution Analysis:")
        
        # Collect all class counts
        rgb_stats = self.validate_data_type("rgb")
        gray_stats = self.validate_data_type("grayscale")
        
        # Combine counts
        total_counts = Counter()
        total_counts.update(rgb_stats['class_counts'])
        total_counts.update(gray_stats['class_counts'])
        
        if not total_counts:
            print("   No labels found")
            return
        
        print(f"   Total Classes with Labels: {len(total_counts)}")
        print(f"   Total Object Instances: {sum(total_counts.values())}")
        
        # Show top classes
        print("\n   Top 10 Classes by Instance Count:")
        for class_id, count in total_counts.most_common(10):
            class_name = self.class_names.get(class_id, f"Class_{class_id}")
            print(f"      {class_name:15} (ID: {class_id:2d}): {count:3d} instances")
    
    def browse_samples(self, data_type="rgb", start_index=0):
        """Interactive browsing of dataset samples"""
        sample_pairs = self.get_sample_files(data_type)
        
        if not sample_pairs:
            print(f"❌ No samples found for {data_type} data")
            return
        
        print(f"\n🔍 Browsing {data_type.upper()} samples ({len(sample_pairs)} total)")
        print("Commands: 'n'=next, 'p'=previous, 'q'=quit, number=jump to index")
        
        current_index = start_index
        
        while True:
            if current_index >= len(sample_pairs):
                current_index = len(sample_pairs) - 1
            elif current_index < 0:
                current_index = 0
            
            img_path, label_path = sample_pairs[current_index]
            
            print(f"\n📍 Sample {current_index + 1}/{len(sample_pairs)}")
            print(f"   Image: {img_path.name}")
            print(f"   Label: {label_path.name}")
            
            # Visualize current sample
            title = f"{data_type.upper()} Sample {current_index + 1}/{len(sample_pairs)}"
            self.visualize_single_sample(img_path, label_path, title)
            
            # Get user input
            command = input("Enter command (n/p/q/number): ").strip().lower()
            
            if command == 'q':
                break
            elif command == 'n':
                current_index += 1
            elif command == 'p':
                current_index -= 1
            elif command.isdigit():
                current_index = int(command) - 1
            else:
                print("Invalid command")


def main():
    """Main function with command line interface"""
    parser = argparse.ArgumentParser(description="FGSat Demo Dataset Visualization Tool")
    parser.add_argument("--dataset", type=str, default=r"G:\ICCSSE\数据集\FGSat_Demo",
                       help="Path to FGSat demo dataset")
    parser.add_argument("--mode", type=str, choices=['validate', 'grid', 'browse', 'single'],
                       default='validate', help="Visualization mode")
    parser.add_argument("--data_type", type=str, choices=['rgb', 'grayscale'], 
                       default='rgb', help="Data type to visualize")
    parser.add_argument("--num_samples", type=int, default=9,
                       help="Number of samples for grid visualization")
    parser.add_argument("--save", type=str, help="Save visualization to file")
    parser.add_argument("--sample_index", type=int, default=0,
                       help="Start index for browsing mode")
    
    args = parser.parse_args()
    
    # Create visualizer
    try:
        visualizer = FGSatDemoVisualizer(args.dataset)
    except Exception as e:
        print(f"❌ Failed to load dataset: {e}")
        return
    
    # Execute based on mode
    if args.mode == 'validate':
        print("🔍 Running dataset validation...")
        visualizer.validate_dataset()
        
    elif args.mode == 'grid':
        print(f"🖼️ Creating grid visualization for {args.data_type} data...")
        visualizer.visualize_grid(args.data_type, args.num_samples, args.save)
        
    elif args.mode == 'browse':
        print(f"👀 Starting interactive browsing for {args.data_type} data...")
        visualizer.browse_samples(args.data_type, args.sample_index)
        
    elif args.mode == 'single':
        # Show first sample
        sample_pairs = visualizer.get_sample_files(args.data_type, 1)
        if sample_pairs:
            img_path, label_path = sample_pairs[0]
            title = f"{args.data_type.upper()} Sample"
            visualizer.visualize_single_sample(img_path, label_path, title, args.save)
        else:
            print(f"❌ No samples found for {args.data_type} data")


if __name__ == "__main__":
    # If run directly, provide interactive menu
    if len(os.sys.argv) == 1:
        print("🎯 FGSat Demo Dataset Visualization Tool")
        print("=" * 50)
        
        dataset_path = input("Enter dataset path (or press Enter for default): ").strip()
        if not dataset_path:
            dataset_path = r"G:\ICCSSE\数据集\FGSat_Demo"
        
        try:
            visualizer = FGSatDemoVisualizer(dataset_path)
        except Exception as e:
            print(f"❌ Failed to load dataset: {e}")
            exit(1)
        
        while True:
            print("\n📋 Available Options:")
            print("1. Validate dataset")
            print("2. Grid visualization (RGB)")
            print("3. Grid visualization (Grayscale)")
            print("4. Browse RGB samples")
            print("5. Browse Grayscale samples")
            print("6. Single sample view")
            print("0. Exit")
            
            choice = input("\nSelect option (0-6): ").strip()
            
            if choice == '0':
                print("👋 Goodbye!")
                break
            elif choice == '1':
                visualizer.validate_dataset()
            elif choice == '2':
                visualizer.visualize_grid("rgb", 9)
            elif choice == '3':
                visualizer.visualize_grid("grayscale", 9)
            elif choice == '4':
                visualizer.browse_samples("rgb")
            elif choice == '5':
                visualizer.browse_samples("grayscale")
            elif choice == '6':
                data_type = input("Enter data type (rgb/grayscale): ").strip().lower()
                if data_type in ['rgb', 'grayscale']:
                    samples = visualizer.get_sample_files(data_type, 1)
                    if samples:
                        img_path, label_path = samples[0]
                        visualizer.visualize_single_sample(img_path, label_path, f"{data_type.upper()} Sample")
                else:
                    print("Invalid data type")
            else:
                print("Invalid option")
    else:
        main()