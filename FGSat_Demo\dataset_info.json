{"dataset_name": "FGSat Pre-Release Dataset", "description": "Pre-release version containing representative samples from standard imaging conditions for fine-grained satellite classification", "version": "1.0", "creation_date": "2025", "imaging_conditions": "Standard conditions only (excludes overexposed, out-of-focus, and noise-artifact images)", "sampling_strategy": "Uniform interval sampling from standard imaging folders", "total_classes": 30, "images_per_class": 20, "rgb_per_class": 10, "grayscale_per_class": 10, "total_images": 600, "rgb_images": 300, "grayscale_images": 300, "image_format": "PNG", "original_resolution": "1920x1080", "classes": {"0": "<PERSON><PERSON><PERSON><PERSON>", "1": "OCO2", "2": "Tango", "3": "POES", "4": "SAC-C", "5": "SeaStar", "6": "Terra", "7": "TOPEX", "8": "TDRS", "9": "Tselina-2", "10": "Double-Star", "11": "Aqua", "12": "Cluster", "13": "Proba-2", "14": "XMM-Newton", "15": "FUSE", "16": "GLAST", "17": "Hinode", "18": "ICON", "19": "MMS", "20": "Swift", "21": "Polar", "22": "LandSat8", "23": "SWAS", "24": "<PERSON>ra", "25": "CALIPSO", "26": "CloudSat", "27": "GOES", "28": "GPM", "29": "ICESat-2"}, "directory_structure": {"RGB": "RGB images for all satellite classes", "RGB_Labels": "YOLO format labels for RGB images", "Grayscale": "Grayscale images for all satellite classes", "Grayscale_Labels": "YOLO format labels for Grayscale images"}}