#!/usr/bin/env python3
"""
FGSat Demo Dataset Annotation Checker and Visualizer

This tool provides comprehensive annotation validation and batch visualization
for manual inspection of the FGSat demo dataset.

Features:
- Automatic annotation validation
- Batch visualization with navigation
- Detailed error reporting
- Interactive inspection interface
"""

import os
import json
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import matplotlib.image as mpimg
from pathlib import Path
from collections import defaultdict
import cv2

class AnnotationChecker:
    def __init__(self, dataset_path):
        self.dataset_path = Path(dataset_path)
        self.dataset_info = self.load_dataset_info()
        self.class_names = self.dataset_info.get('classes', {})
        self.colors = self.generate_colors(len(self.class_names))
        
        # Validation results
        self.validation_results = {
            'total_files': 0,
            'valid_pairs': 0,
            'missing_images': [],
            'missing_labels': [],
            'invalid_labels': [],
            'empty_labels': [],
            'bbox_errors': [],
            'class_errors': [],
            'coordinate_errors': []
        }
        
    def load_dataset_info(self):
        """Load dataset information"""
        info_path = self.dataset_path / "dataset_info.json"
        if info_path.exists():
            with open(info_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                # Convert string keys to integers for class mapping
                if 'classes' in data and isinstance(data['classes'], dict):
                    classes = {}
                    for k, v in data['classes'].items():
                        try:
                            classes[int(k)] = v
                        except ValueError:
                            classes[k] = v
                    data['classes'] = classes
                return data
        return {}
    
    def generate_colors(self, num_colors):
        """Generate distinct colors for different classes"""
        colors = []
        for i in range(num_colors):
            hue = i / num_colors
            # Convert HSV to RGB
            import colorsys
            rgb = colorsys.hsv_to_rgb(hue, 0.8, 0.9)
            colors.append(rgb)
        return colors
    
    def validate_yolo_format(self, label_content, image_width=1920, image_height=1080):
        """Validate YOLO format annotation"""
        errors = []
        
        if not label_content.strip():
            return ['empty_file'], []
        
        bboxes = []
        lines = label_content.strip().split('\n')
        
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            if not line:
                continue
                
            parts = line.split()
            if len(parts) != 5:
                errors.append(f"Line {line_num}: Expected 5 values, got {len(parts)}")
                continue
            
            try:
                class_id = int(parts[0])
                x_center, y_center, width, height = map(float, parts[1:])
                
                # Validate class ID
                if class_id not in self.class_names:
                    errors.append(f"Line {line_num}: Invalid class ID {class_id}")
                
                # Validate coordinates (should be normalized 0-1)
                if not (0 <= x_center <= 1 and 0 <= y_center <= 1):
                    errors.append(f"Line {line_num}: Center coordinates out of range [0,1]")
                
                if not (0 < width <= 1 and 0 < height <= 1):
                    errors.append(f"Line {line_num}: Width/height out of range (0,1]")
                
                # Check if bbox is within image bounds
                x_min = x_center - width / 2
                y_min = y_center - height / 2
                x_max = x_center + width / 2
                y_max = y_center + height / 2
                
                if x_min < 0 or y_min < 0 or x_max > 1 or y_max > 1:
                    errors.append(f"Line {line_num}: Bounding box extends outside image")
                
                bboxes.append({
                    'class_id': class_id,
                    'x_center': x_center,
                    'y_center': y_center,
                    'width': width,
                    'height': height,
                    'line_num': line_num
                })
                
            except ValueError as e:
                errors.append(f"Line {line_num}: Invalid number format - {e}")
        
        return errors, bboxes
    
    def check_image_label_pairs(self, image_dir, label_dir):
        """Check all image-label pairs in directories"""
        image_dir = Path(image_dir)
        label_dir = Path(label_dir)
        
        if not image_dir.exists():
            print(f"❌ Image directory not found: {image_dir}")
            return []
        
        if not label_dir.exists():
            print(f"❌ Label directory not found: {label_dir}")
            return []
        
        # Get all image files
        image_files = list(image_dir.glob("*.png")) + list(image_dir.glob("*.jpg"))
        valid_pairs = []
        
        for image_path in image_files:
            label_path = label_dir / (image_path.stem + ".txt")
            
            self.validation_results['total_files'] += 1
            
            # Check if label file exists
            if not label_path.exists():
                self.validation_results['missing_labels'].append(str(image_path))
                continue
            
            # Check if image can be loaded
            try:
                image = mpimg.imread(str(image_path))
                if image is None:
                    self.validation_results['missing_images'].append(str(image_path))
                    continue
            except Exception as e:
                self.validation_results['missing_images'].append(f"{image_path}: {e}")
                continue
            
            # Validate label content
            try:
                with open(label_path, 'r', encoding='utf-8') as f:
                    label_content = f.read()
                
                errors, bboxes = self.validate_yolo_format(label_content)
                
                if errors:
                    if 'empty_file' in errors:
                        self.validation_results['empty_labels'].append(str(label_path))
                    else:
                        self.validation_results['invalid_labels'].append({
                            'file': str(label_path),
                            'errors': errors
                        })
                    continue
                
                # Valid pair found
                valid_pairs.append({
                    'image_path': image_path,
                    'label_path': label_path,
                    'bboxes': bboxes,
                    'image': image
                })
                self.validation_results['valid_pairs'] += 1
                
            except Exception as e:
                self.validation_results['invalid_labels'].append({
                    'file': str(label_path),
                    'errors': [f"File read error: {e}"]
                })
        
        return valid_pairs
    
    def run_full_validation(self):
        """Run comprehensive validation on the entire dataset"""
        print("🔍 Starting comprehensive annotation validation...")
        print("=" * 60)
        
        # Check RGB images and labels
        print("📷 Checking RGB images and labels...")
        rgb_pairs = self.check_image_label_pairs(
            self.dataset_path / "RGB",
            self.dataset_path / "RGB_Labels"
        )
        
        # Check Grayscale images and labels
        print("⚫ Checking Grayscale images and labels...")
        gray_pairs = self.check_image_label_pairs(
            self.dataset_path / "Grayscale", 
            self.dataset_path / "Grayscale_Labels"
        )
        
        all_pairs = rgb_pairs + gray_pairs
        
        # Print validation summary
        self.print_validation_summary()
        
        return all_pairs
    
    def print_validation_summary(self):
        """Print detailed validation summary"""
        results = self.validation_results
        
        print("\n📊 Validation Summary:")
        print("=" * 60)
        print(f"📁 Total files processed: {results['total_files']}")
        print(f"✅ Valid image-label pairs: {results['valid_pairs']}")
        print(f"❌ Missing labels: {len(results['missing_labels'])}")
        print(f"❌ Missing/corrupted images: {len(results['missing_images'])}")
        print(f"❌ Invalid label files: {len(results['invalid_labels'])}")
        print(f"⚠️  Empty label files: {len(results['empty_labels'])}")
        
        # Print detailed errors
        if results['missing_labels']:
            print(f"\n🔍 Missing label files ({len(results['missing_labels'])}):")
            for missing in results['missing_labels'][:5]:  # Show first 5
                print(f"   • {missing}")
            if len(results['missing_labels']) > 5:
                print(f"   ... and {len(results['missing_labels']) - 5} more")
        
        if results['missing_images']:
            print(f"\n🔍 Missing/corrupted images ({len(results['missing_images'])}):")
            for missing in results['missing_images'][:5]:
                print(f"   • {missing}")
            if len(results['missing_images']) > 5:
                print(f"   ... and {len(results['missing_images']) - 5} more")
        
        if results['invalid_labels']:
            print(f"\n🔍 Invalid label files ({len(results['invalid_labels'])}):")
            for invalid in results['invalid_labels'][:3]:
                print(f"   • {invalid['file']}")
                for error in invalid['errors'][:3]:
                    print(f"     - {error}")
            if len(results['invalid_labels']) > 3:
                print(f"   ... and {len(results['invalid_labels']) - 3} more")
        
        if results['empty_labels']:
            print(f"\n🔍 Empty label files ({len(results['empty_labels'])}):")
            for empty in results['empty_labels'][:5]:
                print(f"   • {empty}")
            if len(results['empty_labels']) > 5:
                print(f"   ... and {len(results['empty_labels']) - 5} more")
        
        # Calculate success rate
        if results['total_files'] > 0:
            success_rate = (results['valid_pairs'] / results['total_files']) * 100
            print(f"\n🎯 Validation Success Rate: {success_rate:.1f}%")
        
        print("=" * 60)
    
    def visualize_batch(self, pairs, batch_size=6, start_idx=0):
        """Visualize a batch of image-annotation pairs"""
        if not pairs:
            print("❌ No valid pairs to visualize")
            return False
        
        end_idx = min(start_idx + batch_size, len(pairs))
        current_batch = pairs[start_idx:end_idx]
        
        # Calculate grid dimensions
        cols = min(3, len(current_batch))
        rows = (len(current_batch) + cols - 1) // cols
        
        fig, axes = plt.subplots(rows, cols, figsize=(15, 5 * rows))
        if rows == 1 and cols == 1:
            axes = [axes]
        elif rows == 1:
            axes = axes
        else:
            axes = axes.flatten()
        
        fig.suptitle(f'Annotation Validation - Batch {start_idx//batch_size + 1} '
                    f'(Images {start_idx + 1}-{end_idx} of {len(pairs)})', 
                    fontsize=16, fontweight='bold')
        
        for i, pair in enumerate(current_batch):
            ax = axes[i]
            
            # Load and display image
            image = pair['image']
            
            # Handle different image formats
            if image.dtype != np.uint8:
                image = (image * 255).astype(np.uint8)
            
            if len(image.shape) == 3 and image.shape[2] == 4:
                image = image[:, :, :3]
            
            ax.imshow(image)
            
            # Draw bounding boxes
            img_height, img_width = image.shape[:2]
            
            for bbox in pair['bboxes']:
                class_id = bbox['class_id']
                class_name = self.class_names.get(class_id, f"Class_{class_id}")
                
                # Convert normalized coordinates to pixel coordinates
                x_center = bbox['x_center'] * img_width
                y_center = bbox['y_center'] * img_height
                width = bbox['width'] * img_width
                height = bbox['height'] * img_height
                
                # Calculate rectangle coordinates (top-left corner)
                x = x_center - width / 2
                y = y_center - height / 2
                
                # Choose color for this class
                color = self.colors[class_id % len(self.colors)]
                
                # Draw bounding box
                rect = patches.Rectangle(
                    (x, y), width, height,
                    linewidth=2, edgecolor=color, facecolor='none'
                )
                ax.add_patch(rect)
                
                # Add class label
                ax.text(x, y - 5, f'{class_name}', 
                       bbox=dict(boxstyle="round,pad=0.3", facecolor=color, alpha=0.7),
                       fontsize=8, color='white', fontweight='bold')
            
            # Set title with filename
            filename = pair['image_path'].name
            ax.set_title(f'{filename}\n{len(pair["bboxes"])} objects', 
                        fontsize=10, fontweight='bold')
            ax.axis('off')
        
        # Hide unused subplots
        for i in range(len(current_batch), len(axes)):
            axes[i].axis('off')
        
        plt.tight_layout()
        plt.show(block=False)
        
        return True
    
    def interactive_visualization(self, pairs, batch_size=6):
        """Interactive batch visualization with navigation"""
        if not pairs:
            print("❌ No valid pairs to visualize")
            return

        total_batches = (len(pairs) + batch_size - 1) // batch_size
        current_batch = 0

        print(f"\n🎨 Interactive Visualization Mode")
        print(f"📊 Total pairs: {len(pairs)}")
        print(f"📦 Batch size: {batch_size}")
        print(f"📄 Total batches: {total_batches}")
        print("\nControls:")
        print("  ENTER or 'n' - Next batch")
        print("  'p' - Previous batch")
        print("  'g' - Go to specific batch")
        print("  's' - Show statistics")
        print("  'f' - Filter by class")
        print("  'r' - Reset filters")
        print("  'q' - Quit")
        print("=" * 50)

        # Track filtered pairs
        filtered_pairs = pairs
        current_filter = "All classes"

        while True:
            # Show current batch
            start_idx = current_batch * batch_size
            success = self.visualize_batch(filtered_pairs, batch_size, start_idx)

            if not success:
                break

            # Get user input
            total_filtered_batches = (len(filtered_pairs) + batch_size - 1) // batch_size
            print(f"\nBatch {current_batch + 1}/{total_filtered_batches} "
                  f"(Images {start_idx + 1}-{min(start_idx + batch_size, len(filtered_pairs))})")
            print(f"Filter: {current_filter}")

            try:
                user_input = input("Command: ").strip().lower()

                plt.close('all')  # Close current plots

                if user_input in ['', 'n', 'next']:
                    current_batch = (current_batch + 1) % total_filtered_batches
                elif user_input in ['p', 'prev', 'previous']:
                    current_batch = (current_batch - 1) % total_filtered_batches
                elif user_input in ['q', 'quit', 'exit']:
                    print("👋 Exiting visualization...")
                    break
                elif user_input.startswith('g'):
                    try:
                        # Extract batch number
                        if len(user_input) > 1:
                            batch_num = int(user_input[1:]) - 1
                        else:
                            batch_num = int(input(f"Enter batch number (1-{total_filtered_batches}): ")) - 1

                        if 0 <= batch_num < total_filtered_batches:
                            current_batch = batch_num
                        else:
                            print(f"❌ Invalid batch number. Must be 1-{total_filtered_batches}")
                    except ValueError:
                        print("❌ Invalid batch number format")
                elif user_input == 's':
                    self.show_statistics(filtered_pairs)
                elif user_input == 'f':
                    filtered_pairs, current_filter = self.filter_by_class(pairs)
                    current_batch = 0  # Reset to first batch
                    total_filtered_batches = (len(filtered_pairs) + batch_size - 1) // batch_size
                elif user_input == 'r':
                    filtered_pairs = pairs
                    current_filter = "All classes"
                    current_batch = 0
                    total_filtered_batches = (len(filtered_pairs) + batch_size - 1) // batch_size
                    print("🔄 Filters reset")
                else:
                    print("❌ Unknown command. Use ENTER/n/p/g/s/f/r/q")

            except KeyboardInterrupt:
                print("\n👋 Exiting visualization...")
                break

        plt.close('all')

    def show_statistics(self, pairs):
        """Show detailed statistics about the annotations"""
        print("\n📊 Annotation Statistics:")
        print("=" * 40)

        # Count objects per class
        class_counts = defaultdict(int)
        total_objects = 0
        images_with_objects = 0

        for pair in pairs:
            if pair['bboxes']:
                images_with_objects += 1
            for bbox in pair['bboxes']:
                class_id = bbox['class_id']
                class_counts[class_id] += 1
                total_objects += 1

        print(f"📁 Total images: {len(pairs)}")
        print(f"📦 Images with objects: {images_with_objects}")
        print(f"🎯 Total objects: {total_objects}")
        print(f"📊 Average objects per image: {total_objects/len(pairs):.2f}")

        print(f"\n🏷️  Objects per class:")
        for class_id, count in sorted(class_counts.items()):
            class_name = self.class_names.get(class_id, f"Class_{class_id}")
            percentage = (count / total_objects) * 100 if total_objects > 0 else 0
            print(f"   {class_name}: {count} ({percentage:.1f}%)")

        print("=" * 40)

    def filter_by_class(self, pairs):
        """Filter pairs by class"""
        print("\n🔍 Available classes:")
        class_counts = defaultdict(int)

        # Count objects per class
        for pair in pairs:
            for bbox in pair['bboxes']:
                class_counts[bbox['class_id']] += 1

        # Show available classes
        for i, (class_id, count) in enumerate(sorted(class_counts.items())):
            class_name = self.class_names.get(class_id, f"Class_{class_id}")
            print(f"   {i+1}. {class_name} ({count} objects)")

        try:
            choice = input("\nEnter class number to filter by (or 'all' for all): ").strip()

            if choice.lower() == 'all':
                return pairs, "All classes"

            class_idx = int(choice) - 1
            available_classes = sorted(class_counts.keys())

            if 0 <= class_idx < len(available_classes):
                target_class_id = available_classes[class_idx]
                class_name = self.class_names.get(target_class_id, f"Class_{target_class_id}")

                # Filter pairs that contain this class
                filtered_pairs = []
                for pair in pairs:
                    for bbox in pair['bboxes']:
                        if bbox['class_id'] == target_class_id:
                            filtered_pairs.append(pair)
                            break

                print(f"✅ Filtered to {len(filtered_pairs)} images containing {class_name}")
                return filtered_pairs, f"Class: {class_name}"
            else:
                print("❌ Invalid class number")
                return pairs, "All classes"

        except ValueError:
            print("❌ Invalid input")
            return pairs, "All classes"

def main():
    """Main function"""
    print("🔍 FGSat Demo Dataset Annotation Checker")
    print("=" * 50)
    
    # Configuration
    dataset_path = r"G:\ICCSSE\数据集\FGSat_Demo"
    
    # Initialize checker
    checker = AnnotationChecker(dataset_path)
    
    # Run validation
    valid_pairs = checker.run_full_validation()
    
    if not valid_pairs:
        print("❌ No valid annotation pairs found. Please check your dataset.")
        return
    
    print(f"\n✅ Found {len(valid_pairs)} valid annotation pairs")
    
    # Ask user if they want to visualize
    while True:
        choice = input("\n🎨 Start interactive visualization? (y/n): ").strip().lower()
        if choice in ['y', 'yes']:
            # Ask for batch size
            try:
                batch_size = input("📦 Batch size (default 6): ").strip()
                batch_size = int(batch_size) if batch_size else 6
                batch_size = max(1, min(batch_size, 12))  # Limit between 1-12
            except ValueError:
                batch_size = 6
            
            checker.interactive_visualization(valid_pairs, batch_size)
            break
        elif choice in ['n', 'no']:
            print("👋 Validation complete. Exiting...")
            break
        else:
            print("❌ Please enter 'y' or 'n'")

if __name__ == "__main__":
    main()
