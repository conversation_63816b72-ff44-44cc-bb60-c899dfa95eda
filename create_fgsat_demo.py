#!/usr/bin/env python3
"""
FGSat Demo Dataset Creator

Creates a demonstration version of the FGSat dataset for fine-grained satellite classification.
The demo includes 20 images per satellite class (10 RGB + 10 Grayscale).
Total: 30 classes × 20 images = 600 images

Key Features:
- Uniform sampling: Images are selected with even intervals, not randomly
- Standard data only: Uses only 1-RGB-700 and 2-灰度-700 folders (no augmented data)
- Consistent naming: SpacecraftName_ImageType_Index.png format

Output structure:
FGSat_Demo/
├── RGB/           # 300 RGB images (30 classes × 10 images each)
├── RGB_Labels/    # 300 YOLO format label files for RGB images
├── Grayscale/     # 300 Grayscale images (30 classes × 10 images each)
└── Grayscale_Labels/  # 300 YOLO format label files for Grayscale images

Each image is named using the spacecraft name for easy identification.
"""

import os
import json
import shutil
import random
from pathlib import Path
from collections import defaultdict
import cv2
import numpy as np
from tqdm import tqdm
import matplotlib.pyplot as plt
import matplotlib.image as mpimg
import sys

# Note: No random seed needed as we use deterministic uniform sampling

# Configuration - Updated paths for G:\ICCSSE\数据集
CONFIG = {
    "source_dir": r"G:\ICCSSE\数据集\AA-最终版自己的数据库",
    "output_dir": r"G:\ICCSSE\数据集\FGSat_Demo",
    "images_per_class": 20,  # Total images per satellite class
    "rgb_per_class": 10,     # RGB images per class
    "gray_per_class": 10,    # Grayscale images per class
    "interactive_mode": True,  # Enable human-in-the-loop confirmation
    "preview_size": (10, 6),   # Preview window size (width, height)
}

class FGSatDemoCreator:
    def __init__(self, config):
        self.config = config
        self.source_dir = Path(config["source_dir"])
        self.output_dir = Path(config["output_dir"])
        self.images_per_class = config["images_per_class"]
        self.rgb_per_class = config["rgb_per_class"]
        self.gray_per_class = config["gray_per_class"]
        self.interactive_mode = config.get("interactive_mode", False)
        self.preview_size = config.get("preview_size", (10, 6))
        
        # Create output directories for images and labels
        self.rgb_output_dir = self.output_dir / "RGB"
        self.gray_output_dir = self.output_dir / "Grayscale"
        self.rgb_labels_dir = self.output_dir / "RGB_Labels"
        self.gray_labels_dir = self.output_dir / "Grayscale_Labels"
        
        # Satellite class mapping
        self.spacecraft_classes = {}
        self.stats = {
            "total_satellites": 0,
            "rgb_images": 0,
            "gray_images": 0,
            "total_images": 0
        }
    
    def clean_spacecraft_name(self, folder_name):
        """
        Extract and clean spacecraft name from folder name
        Remove numeric prefixes and standardize naming
        """
        # Remove numeric prefix (e.g., "1-Aquarius" -> "Aquarius")
        if '-' in folder_name:
            name = folder_name.split('-', 1)[1]
        else:
            name = folder_name
        
        # Clean up special characters and standardize
        name = name.replace('_', '-')
        name = name.strip()
        
        return name
    
    def setup_directories(self):
        """Create necessary directory structure"""
        print("📁 Setting up directory structure...")
        
        # Remove existing demo directory if exists
        if self.output_dir.exists():
            print(f"   Removing existing directory: {self.output_dir}")
            shutil.rmtree(self.output_dir)
        
        # Create new directories for images and labels
        self.rgb_output_dir.mkdir(parents=True, exist_ok=True)
        self.gray_output_dir.mkdir(parents=True, exist_ok=True)
        self.rgb_labels_dir.mkdir(parents=True, exist_ok=True)
        self.gray_labels_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"   ✅ Created RGB images directory: {self.rgb_output_dir}")
        print(f"   ✅ Created RGB labels directory: {self.rgb_labels_dir}")
        print(f"   ✅ Created Grayscale images directory: {self.gray_output_dir}")
        print(f"   ✅ Created Grayscale labels directory: {self.gray_labels_dir}")
    
    def discover_spacecraft_folders(self):
        """Discover all spacecraft folders in the source directory"""
        print("🔍 Discovering spacecraft folders...")
        
        if not self.source_dir.exists():
            print(f"❌ Source directory does not exist: {self.source_dir}")
            return []
        
        spacecraft_folders = []
        for item in self.source_dir.iterdir():
            if item.is_dir() and not item.name.startswith("ZZ-"):
                spacecraft_folders.append(item)
        
        # Sort folders by name for consistent ordering
        spacecraft_folders.sort(key=lambda x: x.name)
        
        print(f"   Found {len(spacecraft_folders)} spacecraft folders")
        
        # Display discovered spacecraft
        for i, folder in enumerate(spacecraft_folders):
            clean_name = self.clean_spacecraft_name(folder.name)
            self.spacecraft_classes[i] = clean_name
            print(f"      {i:2d}: {folder.name} -> {clean_name}")
        
        return spacecraft_folders
    
    def collect_spacecraft_images(self, spacecraft_folder):
        """
        Collect available images from a spacecraft folder
        Only collects from standard folders (1-RGB-700 and 2-灰度-700)
        Returns dict with RGB and Grayscale image lists sorted by filename
        """
        images = {"rgb": [], "grayscale": []}
        
        # Define folder patterns - ONLY standard folders for DEMO version
        rgb_pattern = "1-RGB-700"
        gray_pattern = "2-灰度-700"
        
        # Collect RGB images from standard folder only
        rgb_subfolder = spacecraft_folder / rgb_pattern
        if rgb_subfolder.exists():
            # Get all PNG images in the subfolder
            png_files = list(rgb_subfolder.glob("*.png"))
            # Filter to only include camera images and sort by filename
            camera_files = [f for f in png_files if "camera" in f.name]
            camera_files.sort(key=lambda x: x.name)  # Sort for consistent ordering
            images["rgb"] = camera_files
        
        # Collect Grayscale images from standard folder only
        gray_subfolder = spacecraft_folder / gray_pattern
        if gray_subfolder.exists():
            # Get all PNG images in the subfolder
            png_files = list(gray_subfolder.glob("*.png"))
            # Filter to only include camera images and sort by filename
            camera_files = [f for f in png_files if "camera" in f.name]
            camera_files.sort(key=lambda x: x.name)  # Sort for consistent ordering
            images["grayscale"] = camera_files
        
        return images
    
    def get_sampling_candidates(self, image_list, sample_count, offset=False):
        """
        Get candidate images for uniform sampling with backup options
        
        Args:
            image_list: List of image paths (should be sorted)
            sample_count: Number of images to sample
            offset: If True, use offset to avoid overlapping with another sampling
        
        Returns:
            List of tuples: [(primary_image, backup_images_list), ...]
        """
        if not image_list or sample_count <= 0:
            return []
        
        total_images = len(image_list)
        
        if sample_count >= total_images:
            return [(img, []) for img in image_list]
        
        candidates = []
        
        # Calculate uniform intervals
        if sample_count == 1:
            # If only one sample needed, take the middle one
            selected_indices = [total_images // 2]
        else:
            # Calculate step size for uniform distribution
            step = total_images / sample_count
            
            if offset:
                # Add offset to avoid same indices as RGB sampling
                start_offset = step / 2
            else:
                start_offset = step / 2
            
            # Generate uniformly spaced indices
            selected_indices = []
            for i in range(sample_count):
                index = int(start_offset + i * step)
                # Ensure index is within bounds
                index = min(index, total_images - 1)
                selected_indices.append(index)
        
        # Remove duplicates and sort
        selected_indices = sorted(list(set(selected_indices)))
        
        # For each selected index, create candidate with backups
        for idx in selected_indices:
            primary_image = image_list[idx]
            
            # Generate backup options (nearby images)
            backup_images = []
            backup_range = min(10, total_images // sample_count // 2)  # Search within reasonable range
            
            for offset_val in range(1, backup_range + 1):
                # Try images after the primary
                if idx + offset_val < total_images:
                    backup_images.append(image_list[idx + offset_val])
                # Try images before the primary
                if idx - offset_val >= 0:
                    backup_images.append(image_list[idx - offset_val])
                    
                # Limit backup options
                if len(backup_images) >= 20:
                    break
            
            candidates.append((primary_image, backup_images))
        
        return candidates
    
    def preview_image(self, image_path, title=""):
        """
        Display image preview for user confirmation
        
        Args:
            image_path: Path to image file
            title: Title for the preview window
            
        Returns:
            PIL Image object or None if failed
        """
        try:
            import matplotlib.image as mpimg
            image = mpimg.imread(str(image_path))
            
            if image is not None:
                # Handle format conversion
                if image.dtype != np.uint8:
                    image = (image * 255).astype(np.uint8)
                
                # Handle RGBA to RGB conversion
                if len(image.shape) == 3 and image.shape[2] == 4:
                    image = image[:, :, :3]  # Remove alpha channel
                
                # Display image
                plt.figure(figsize=self.preview_size)
                plt.imshow(image)
                plt.title(f"{title}\n{image_path.name}", fontsize=12)
                plt.axis('off')
                plt.tight_layout()
                plt.show(block=False)
                plt.pause(0.1)  # Allow GUI to update
                
                return image
            
        except Exception as e:
            print(f"   ⚠️ Error previewing image: {e}")
        
        return None
    
    def get_user_confirmation(self, spacecraft_name, image_type, sample_idx, total_samples):
        """
        Get user confirmation for image selection
        
        Returns:
            True if confirmed (Enter), False if rejected (Space), 'quit' if user wants to exit interactive mode
        """
        print(f"\n📋 {spacecraft_name} - {image_type.upper()} Sample {sample_idx + 1}/{total_samples}")
        print("   👀 Preview window opened (check your screen)")
        print("   ✅ Press ENTER to confirm this image")
        print("   ❌ Press SPACE to reject and try next image")
        print("   ⏹️  Press 'q' to quit interactive mode")
        
        while True:
            try:
                user_input = input("   Your choice: ").strip().lower()
                
                if user_input == '' or user_input == 'enter':
                    return True
                elif user_input == ' ' or user_input == 'space':
                    return False
                elif user_input == 'q' or user_input == 'quit':
                    print("   🔄 Switching to automatic mode...")
                    return 'quit'
                else:
                    print("   ❗ Invalid input. Use ENTER (confirm), SPACE (reject), or 'q' (quit)")
                    
            except KeyboardInterrupt:
                print("\n   🔄 Switching to automatic mode...")
                return 'quit'
    
    def interactive_select_image(self, candidates, spacecraft_name, image_type, sample_idx, total_samples):
        """
        Interactively select image with user confirmation
        
        Args:
            candidates: Tuple of (primary_image, backup_images)
            spacecraft_name: Name of spacecraft
            image_type: 'rgb' or 'grayscale'
            sample_idx: Current sample index
            total_samples: Total samples to select
            
        Returns:
            Tuple: (selected_image_path, continue_interactive_mode)
                   continue_interactive_mode is False if user chose to quit interactive mode
        """
        primary_image, backup_images = candidates
        all_candidates = [primary_image] + backup_images
        
        for i, image_path in enumerate(all_candidates):
            # Show preview
            image_preview = self.preview_image(image_path, 
                f"{spacecraft_name} {image_type.upper()} - Option {i+1}/{len(all_candidates)}")
            
            if image_preview is None:
                print(f"   ⚠️ Cannot preview {image_path.name}, skipping...")
                continue
            
            # Get user confirmation
            confirmation = self.get_user_confirmation(spacecraft_name, image_type, sample_idx, total_samples)
            
            # Close preview window
            plt.close()
            
            if confirmation == True:
                print(f"   ✅ Selected: {image_path.name}")
                return image_path, True  # Continue interactive mode
            elif confirmation == 'quit':
                print(f"   🔄 Auto-selecting: {image_path.name}")
                return image_path, False  # Exit interactive mode
            else:
                print(f"   ❌ Rejected: {image_path.name}")
                if i < len(all_candidates) - 1:
                    print(f"   🔄 Trying next option...")
        
        # If all candidates rejected, return the primary image
        print(f"   ⚠️ All options rejected, using primary selection: {primary_image.name}")
        return primary_image, True  # Continue interactive mode
    
    def convert_to_grayscale(self, rgb_image_path):
        """
        Convert RGB image to grayscale if needed
        Returns the grayscale image as numpy array
        """
        try:
            # Read the image
            image = cv2.imread(str(rgb_image_path))
            if image is None:
                return None
            
            # Convert BGR to RGB (OpenCV uses BGR by default)
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            
            # Convert to grayscale
            gray_image = cv2.cvtColor(image_rgb, cv2.COLOR_RGB2GRAY)
            
            return gray_image
        except Exception as e:
            print(f"      ⚠️ Error converting image to grayscale: {e}")
            return None
    
    def unity_to_yolo_bbox(self, origin, dimension, img_width=1920, img_height=1080):
        """
        Convert Unity Solo bounding box to YOLO format
        
        Args:
            origin: [x_min, y_min] from Unity
            dimension: [width, height] from Unity
            img_width: Image width (default 1920)
            img_height: Image height (default 1080)
        
        Returns:
            [center_x, center_y, width, height] normalized to 0-1
        """
        x_min, y_min = origin
        width, height = dimension
        
        # Calculate center point
        center_x = x_min + width / 2
        center_y = y_min + height / 2
        
        # Normalize coordinates
        center_x_norm = center_x / img_width
        center_y_norm = center_y / img_height
        width_norm = width / img_width
        height_norm = height / img_height
        
        return [center_x_norm, center_y_norm, width_norm, height_norm]
    
    def convert_json_to_yolo(self, json_path, spacecraft_name):
        """
        Convert Unity Solo JSON annotation to YOLO format
        
        Args:
            json_path: Path to JSON file
            spacecraft_name: Name of spacecraft for class mapping
        
        Returns:
            YOLO format string or None if conversion fails
        """
        try:
            with open(json_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            yolo_lines = []
            captures = data.get("captures", [])
            
            if captures and "annotations" in captures[0]:
                for annotation in captures[0]["annotations"]:
                    if annotation.get("@type") == "type.unity.com/unity.solo.BoundingBox2DAnnotation":
                        values = annotation.get("values", [])
                        
                        for value in values:
                            label_name = value.get("labelName")
                            origin = value.get("origin")
                            dimension = value.get("dimension")
                            
                            if label_name and origin and dimension:
                                # Find class ID for this spacecraft
                                class_id = None
                                for cid, cname in self.spacecraft_classes.items():
                                    if cname == spacecraft_name:
                                        class_id = cid
                                        break
                                
                                if class_id is not None:
                                    # Convert bounding box to YOLO format
                                    yolo_bbox = self.unity_to_yolo_bbox(origin, dimension)
                                    
                                    # Format as YOLO line: class_id center_x center_y width height
                                    line = f"{class_id} {' '.join(f'{coord:.6f}' for coord in yolo_bbox)}"
                                    yolo_lines.append(line)
            
            return '\n'.join(yolo_lines) if yolo_lines else ""
            
        except Exception as e:
            print(f"      ⚠️ Error converting JSON to YOLO: {e}")
            return None
    
    def save_image_and_label(self, source_path, image_output_path, label_output_path, spacecraft_name, image_type, index):
        """
        Save image with standardized naming and corresponding YOLO label
        Args:
            source_path: Source image path
            image_output_path: Output directory path for images
            label_output_path: Output directory path for labels
            spacecraft_name: Clean spacecraft name
            image_type: 'rgb' or 'grayscale'
            index: Image index for this class
        """
        # Create filenames: SpacecraftName_ImageType_Index.png/.txt
        base_filename = f"{spacecraft_name}_{image_type}_{index:03d}"
        img_filename = f"{base_filename}.png"
        label_filename = f"{base_filename}.txt"
        
        img_dst_path = image_output_path / img_filename
        label_dst_path = label_output_path / label_filename
        
        try:
            # Save image
            if image_type == "rgb":
                # Copy RGB image directly
                shutil.copy2(source_path, img_dst_path)
            else:
                # For grayscale, check if it's actually a grayscale image or needs conversion
                if "灰度" in str(source_path):
                    # Copy grayscale image directly
                    shutil.copy2(source_path, img_dst_path)
                else:
                    # Convert RGB to grayscale
                    gray_image = self.convert_to_grayscale(source_path)
                    if gray_image is not None:
                        cv2.imwrite(str(img_dst_path), gray_image)
                    else:
                        print(f"      ⚠️ Failed to convert image: {source_path}")
                        return False
            
            # Find corresponding JSON file and convert to YOLO
            json_path = source_path.with_suffix('.json').with_name(
                source_path.stem.replace('.camera', '') + '.frame_data.json'
            )
            
            if json_path.exists():
                yolo_content = self.convert_json_to_yolo(json_path, spacecraft_name)
                if yolo_content is not None:
                    # Save YOLO label file
                    with open(label_dst_path, 'w', encoding='utf-8') as f:
                        f.write(yolo_content)
                else:
                    print(f"      ⚠️ Failed to convert JSON label: {json_path}")
                    # Create empty label file
                    label_dst_path.touch()
            else:
                print(f"      ⚠️ JSON file not found: {json_path}")
                # Create empty label file
                label_dst_path.touch()
            
            return True
            
        except Exception as e:
            print(f"      ❌ Error saving image/label {base_filename}: {e}")
            return False
    
    def process_spacecraft(self, spacecraft_folder):
        """Process a single spacecraft folder"""
        spacecraft_name = self.clean_spacecraft_name(spacecraft_folder.name)
        print(f"   Processing {spacecraft_name}...")
        
        # Collect all available images
        images = self.collect_spacecraft_images(spacecraft_folder)
        
        rgb_available = len(images["rgb"])
        gray_available = len(images["grayscale"])
        
        print(f"      Available: {rgb_available} RGB, {gray_available} Grayscale")
        
        if rgb_available == 0 and gray_available == 0:
            print(f"      ⚠️ No images found for {spacecraft_name}")
            return False
        
        # Sample images with or without interactive confirmation
        rgb_selected = []
        gray_selected = []
        
        # Sample RGB images
        if rgb_available > 0:
            sample_count = min(self.rgb_per_class, rgb_available)
            if self.interactive_mode:
                rgb_candidates = self.get_sampling_candidates(images["rgb"], sample_count)
                for idx, candidates in enumerate(rgb_candidates):
                    selected_image, continue_interactive = self.interactive_select_image(
                        candidates, spacecraft_name, "rgb", idx, sample_count)
                    if selected_image:
                        rgb_selected.append(selected_image)
                        
                    # Check if user quit interactive mode
                    if not continue_interactive:
                        self.interactive_mode = False
                        # Complete remaining selections automatically
                        remaining_candidates = rgb_candidates[len(rgb_selected):]
                        for remaining_candidate in remaining_candidates:
                            rgb_selected.append(remaining_candidate[0])  # Use primary image
                        break
            else:
                # Automatic selection (original logic)
                rgb_candidates = self.get_sampling_candidates(images["rgb"], sample_count)
                rgb_selected = [candidate[0] for candidate in rgb_candidates]
        
        # Sample Grayscale images
        if gray_available > 0:
            sample_count = min(self.gray_per_class, gray_available)
            if self.interactive_mode:
                gray_candidates = self.get_sampling_candidates(images["grayscale"], sample_count)
                for idx, candidates in enumerate(gray_candidates):
                    selected_image, continue_interactive = self.interactive_select_image(
                        candidates, spacecraft_name, "grayscale", idx, sample_count)
                    if selected_image:
                        gray_selected.append(selected_image)
                        
                    # Check if user quit interactive mode
                    if not continue_interactive:
                        self.interactive_mode = False
                        # Complete remaining selections automatically
                        remaining_candidates = gray_candidates[len(gray_selected):]
                        for remaining_candidate in remaining_candidates:
                            gray_selected.append(remaining_candidate[0])  # Use primary image
                        break
            else:
                # Automatic selection
                gray_candidates = self.get_sampling_candidates(images["grayscale"], sample_count)
                gray_selected = [candidate[0] for candidate in gray_candidates]
        elif rgb_available > 0:
            # If no grayscale images, use RGB images and convert them
            sample_count = min(self.gray_per_class, rgb_available)
            if self.interactive_mode:
                gray_candidates = self.get_sampling_candidates(images["rgb"], sample_count, offset=True)
                for idx, candidates in enumerate(gray_candidates):
                    selected_image, continue_interactive = self.interactive_select_image(
                        candidates, spacecraft_name, "grayscale", idx, sample_count)
                    if selected_image:
                        gray_selected.append(selected_image)
                        
                    # Check if user quit interactive mode
                    if not continue_interactive:
                        self.interactive_mode = False
                        # Complete remaining selections automatically
                        remaining_candidates = gray_candidates[len(gray_selected):]
                        for remaining_candidate in remaining_candidates:
                            gray_selected.append(remaining_candidate[0])  # Use primary image
                        break
            else:
                # Automatic selection
                gray_candidates = self.get_sampling_candidates(images["rgb"], sample_count, offset=True)
                gray_selected = [candidate[0] for candidate in gray_candidates]
        
        # Save RGB images and labels
        rgb_saved = 0
        for i, img_path in enumerate(rgb_selected):
            if self.save_image_and_label(img_path, self.rgb_output_dir, self.rgb_labels_dir, spacecraft_name, "rgb", i):
                rgb_saved += 1
        
        # Save Grayscale images and labels
        gray_saved = 0
        for i, img_path in enumerate(gray_selected):
            if self.save_image_and_label(img_path, self.gray_output_dir, self.gray_labels_dir, spacecraft_name, "grayscale", i):
                gray_saved += 1
        
        print(f"      ✅ Saved: {rgb_saved} RGB, {gray_saved} Grayscale")
        
        # Update statistics
        self.stats["rgb_images"] += rgb_saved
        self.stats["gray_images"] += gray_saved
        self.stats["total_images"] += rgb_saved + gray_saved
        
        return True
    
    def create_dataset_info(self):
        """Create dataset information file"""
        print("📝 Creating dataset information file...")
        
        dataset_info = {
            "dataset_name": "FGSat Demo Dataset",
            "description": "Demonstration version of the FGSat dataset for fine-grained satellite classification",
            "version": "1.0",
            "creation_date": "2024",
            "total_classes": len(self.spacecraft_classes),
            "images_per_class": self.images_per_class,
            "rgb_per_class": self.rgb_per_class,
            "grayscale_per_class": self.gray_per_class,
            "total_images": self.stats["total_images"],
            "rgb_images": self.stats["rgb_images"],
            "grayscale_images": self.stats["gray_images"],
            "image_format": "PNG",
            "original_resolution": "1920x1080",
            "classes": self.spacecraft_classes,
            "directory_structure": {
                "RGB": "RGB images for all satellite classes",
                "RGB_Labels": "YOLO format labels for RGB images", 
                "Grayscale": "Grayscale images for all satellite classes",
                "Grayscale_Labels": "YOLO format labels for Grayscale images"
            },
            "naming_convention": "SpacecraftName_ImageType_Index.png",
            "sampling_strategy": "uniform_interval",
            "data_source_folders": ["1-RGB-700", "2-灰度-700"],
            "excludes_augmented_data": True,
            "source_directory": str(self.source_dir),
            "output_directory": str(self.output_dir)
        }
        
        # Save as JSON file
        info_path = self.output_dir / "dataset_info.json"
        with open(info_path, 'w', encoding='utf-8') as f:
            json.dump(dataset_info, f, indent=2, ensure_ascii=False)
        
        print(f"   ✅ Dataset info saved: {info_path}")
    
    def create_readme(self):
        """Create README file for the demo dataset"""
        print("📄 Creating README file...")
        
        readme_content = f"""# FGSat Demo Dataset

## Overview
This is a demonstration version of the FGSat dataset for fine-grained satellite classification research. The dataset is specifically designed for testing and showcasing satellite recognition algorithms.

## Dataset Statistics
- **Total Classes**: {len(self.spacecraft_classes)} satellite types
- **Total Images**: {self.stats['total_images']}
- **RGB Images**: {self.stats['rgb_images']}
- **Grayscale Images**: {self.stats['gray_images']}
- **Images per Class**: {self.images_per_class} ({self.rgb_per_class} RGB + {self.gray_per_class} Grayscale)
- **Image Resolution**: 1920×1080 pixels
- **Image Format**: PNG

## Directory Structure
```
FGSat_Demo/
├── RGB/                    # {self.stats['rgb_images']} RGB images
├── RGB_Labels/             # {self.stats['rgb_images']} YOLO format label files for RGB
├── Grayscale/             # {self.stats['gray_images']} Grayscale images  
├── Grayscale_Labels/      # {self.stats['gray_images']} YOLO format label files for Grayscale
├── dataset_info.json     # Detailed dataset information
└── README.md            # This file
```

## Satellite Classes
The demo includes the following {len(self.spacecraft_classes)} satellite types from various space missions:

"""
        
        # Add satellite class list in a more organized way
        for class_id, class_name in self.spacecraft_classes.items():
            readme_content += f"{class_id+1:2d}. **{class_name}**\n"
        
        readme_content += f"""
## File Naming Convention
Images are systematically named using the format: `SpacecraftName_ImageType_Index.png`

**Examples:**
- `Aquarius_rgb_001.png` - First RGB image of Aquarius satellite
- `Aquarius_rgb_001.txt` - Corresponding YOLO label file
- `Terra_grayscale_005.png` - Fifth grayscale image of Terra satellite  
- `Terra_grayscale_005.txt` - Corresponding YOLO label file
- `ICESat-2_rgb_008.png` - Eighth RGB image of ICESat-2 satellite
- `ICESat-2_rgb_008.txt` - Corresponding YOLO label file

## YOLO Label Format
Each `.txt` file contains object annotations in YOLO format:
```
class_id center_x center_y width height
```
- `class_id`: Integer (0-29) representing satellite class
- `center_x, center_y`: Normalized center coordinates (0.0-1.0)
- `width, height`: Normalized bounding box dimensions (0.0-1.0)

**Example label file content:**
```
0 0.512 0.423 0.245 0.356
```

## Usage Examples
This demo dataset can be used for:
- **Algorithm Testing**: Quick validation of satellite classification models
- **Educational Purposes**: Teaching fine-grained visual classification
- **Research Demonstrations**: Showcasing satellite recognition capabilities
- **Preliminary Development**: Initial testing before using the full dataset

## Technical Details
- **Sampling Strategy**: Uniform interval sampling (not random) for representative coverage
- **Data Source**: Only standard folders (1-RGB-700, 2-灰度-700) - excludes augmented data
- **Quality Control**: Only camera images are included (excludes metadata files)
- **Interval Calculation**: For 10 samples from 700 images, selects every ~70th image
- **Grayscale Conversion**: RGB images are converted to grayscale when native grayscale images are unavailable

## Loading the Dataset
### Python Example
```python
import os
from pathlib import Path

# Load RGB images
rgb_dir = Path("FGSat_Demo/RGB")
rgb_images = list(rgb_dir.glob("*.png"))
print(f"Found {{len(rgb_images)}} RGB images")

# Load Grayscale images  
gray_dir = Path("FGSat_Demo/Grayscale")
gray_images = list(gray_dir.glob("*.png"))
print(f"Found {{len(gray_images)}} Grayscale images")

# Extract class names from filenames
classes = set([img.stem.split('_')[0] for img in rgb_images])
print(f"Found {{len(classes)}} satellite classes")
```

## Dataset Characteristics
- **Fine-Grained Classification**: Focuses on distinguishing between similar satellite types
- **Multi-Modal**: Includes both RGB and grayscale imagery
- **Realistic Rendering**: High-fidelity synthetic images with realistic lighting and backgrounds
- **Diverse Viewpoints**: Multiple camera angles and distances for each satellite
- **Mission Variety**: Covers Earth observation, scientific, and communication satellites

## Original Dataset
This demo is derived from the comprehensive FGSat dataset, which contains extensive satellite imagery for advanced fine-grained classification research. The full dataset includes:
- Multiple image conditions (normal, overexposed, blurred, noisy)
- Temporal sequences showing satellite motion
- Detailed metadata including camera parameters and satellite poses

## Citation
If you use this dataset in your research, please cite our paper:
```bibtex
@inproceedings{{fgsat2024,
  title={{FGSat: A New Dataset and Performance Benchmark for Fine-Grained Satellite Classification}},
  author={{[Authors]}},
  booktitle={{[Conference]}},
  year={{2024}}
}}
```

## Contact Information
For questions, issues, or collaboration opportunities, please contact:
- **Email**: [Contact Email]
- **GitHub**: [Repository URL]
- **Paper**: [Paper URL when available]

## License
[License information to be added]

---
**Generated Information:**
- Created: {self.stats['total_images']} images from {len(self.spacecraft_classes)} satellite classes
- Sampling: Uniform intervals (deterministic, reproducible)
- Data Source: Standard folders only (excludes augmented data)
- Generated by: FGSat Demo Dataset Creator v1.0
- Creation Date: 2024

*This demo dataset provides a representative sample of the full FGSat dataset for rapid prototyping and evaluation.*
"""
        
        # Save README file
        readme_path = self.output_dir / "README.md"
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        print(f"   ✅ README saved: {readme_path}")
    
    def print_statistics(self):
        """Print final statistics"""
        print("\n📊 FGSat Demo Dataset Creation Summary:")
        print("=" * 60)
        print(f"📁 Source Directory: {self.source_dir}")
        print(f"📁 Output Directory: {self.output_dir}")
        print(f"🛰️  Total Satellite Classes: {len(self.spacecraft_classes)}")
        print(f"📷 Images per Class: {self.images_per_class} ({self.rgb_per_class} RGB + {self.gray_per_class} Grayscale)")
        print(f"📊 Total Images Created: {self.stats['total_images']}")
        print(f"   • RGB Images: {self.stats['rgb_images']}")
        print(f"   • Grayscale Images: {self.stats['gray_images']}")
        print(f"📐 Sampling Strategy: Uniform interval (deterministic)")
        
        # Verify expected vs actual counts
        expected_total = len(self.spacecraft_classes) * self.images_per_class
        expected_rgb = len(self.spacecraft_classes) * self.rgb_per_class
        expected_gray = len(self.spacecraft_classes) * self.gray_per_class
        
        print(f"\n🎯 Expected vs Actual Results:")
        print(f"   Total: {expected_total} expected → {self.stats['total_images']} actual")
        print(f"   RGB: {expected_rgb} expected → {self.stats['rgb_images']} actual")
        print(f"   Grayscale: {expected_gray} expected → {self.stats['gray_images']} actual")
        
        # Success rate
        success_rate = (self.stats['total_images'] / expected_total) * 100 if expected_total > 0 else 0
        print(f"   Success Rate: {success_rate:.1f}%")
        
        # Sampling details
        if len(self.spacecraft_classes) > 0:
            print(f"\n📊 Sampling Details:")
            print(f"   Standard folders only: 1-RGB-700, 2-灰度-700")
            print(f"   For 700 images → 10 samples: ~every 70th image")
            print(f"   Uniform intervals ensure representative temporal coverage")
    
    def run(self):
        """Execute the demo dataset creation process"""
        print("🚀 FGSat Demo Dataset Creator")
        print("=" * 60)
        print("Creating a demonstration version of the FGSat dataset")
        print("for fine-grained satellite classification research.")
        print("=" * 60)
        print(f"📁 Source Directory: {self.source_dir}")
        print(f"📁 Output Directory: {self.output_dir}")
        print(f"📊 Target: {self.images_per_class} images per class ({self.rgb_per_class} RGB + {self.gray_per_class} Grayscale)")
        print(f"📐 Sampling: Uniform intervals for representative coverage")
        print(f"📁 Data Source: Standard folders only (1-RGB-700, 2-灰度-700)")
        
        if self.interactive_mode:
            print(f"🤖 Mode: Interactive (Human-in-the-loop)")
            print("   • You will preview each candidate image")
            print("   • Press ENTER to confirm, SPACE to reject")
            print("   • Press 'q' to switch to automatic mode")
        else:
            print(f"🤖 Mode: Automatic")
            
        print()
        
        # Step 1: Setup directories
        self.setup_directories()
        
        # Step 2: Discover spacecraft folders
        spacecraft_folders = self.discover_spacecraft_folders()
        
        if len(spacecraft_folders) == 0:
            print("❌ No spacecraft folders found!")
            print("   Please check the source directory path.")
            return False
        
        self.stats["total_satellites"] = len(spacecraft_folders)
        
        # Step 3: Process each spacecraft
        print(f"\n🏭 Processing {len(spacecraft_folders)} spacecraft types...")
        print("-" * 50)
        
        successful_count = 0
        for spacecraft_folder in tqdm(spacecraft_folders, desc="Creating demo images", ncols=80):
            if self.process_spacecraft(spacecraft_folder):
                successful_count += 1
        
        print(f"\n✅ Successfully processed {successful_count}/{len(spacecraft_folders)} spacecraft")
        
        # Step 4: Create dataset documentation
        print("\n📝 Generating dataset documentation...")
        self.create_dataset_info()
        self.create_readme()
        
        # Step 5: Print final statistics
        self.print_statistics()
        
        print(f"\n🎉 FGSat Demo Dataset Created Successfully!")
        print("=" * 60)
        print(f"📂 Demo Location: {self.output_dir}")
        print(f"🖼️  RGB Images: {self.stats['rgb_images']} (in RGB/ folder)")
        print(f"🏷️  RGB Labels: {self.stats['rgb_images']} YOLO files (in RGB_Labels/ folder)")
        print(f"⚫ Grayscale Images: {self.stats['gray_images']} (in Grayscale/ folder)")
        print(f"🏷️  Grayscale Labels: {self.stats['gray_images']} YOLO files (in Grayscale_Labels/ folder)")
        print(f"📄 Documentation: README.md and dataset_info.json included")
        print("\nThe demo dataset is ready for YOLO training and satellite classification research!")
        
        return True


def main():
    """Main function"""
    try:
        # Ask user about interactive mode
        print("🎯 FGSat Demo Dataset Creator")
        print("=" * 40)
        print("Choose creation mode:")
        print("1. Interactive mode (Human-in-the-loop) - Preview and confirm each image")
        print("2. Automatic mode - Use algorithm selection without confirmation")
        print()
        
        while True:
            choice = input("Enter your choice (1 or 2): ").strip()
            if choice == '1':
                CONFIG["interactive_mode"] = True
                break
            elif choice == '2':
                CONFIG["interactive_mode"] = False
                break
            else:
                print("❗ Please enter 1 or 2")
        
        creator = FGSatDemoCreator(CONFIG)
        success = creator.run()
        
        if success:
            print("\n🎊 Demo dataset creation completed successfully!")
            print("You can now use the FGSat demo for research and development.")
        else:
            print("\n❌ Demo dataset creation failed!")
            print("Please check the error messages above and try again.")
            
    except KeyboardInterrupt:
        print("\n⏹️  Operation cancelled by user")
    except Exception as e:
        print(f"\n❌ Unexpected error occurred: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()