# FGSat 数据集标注检查工具使用指南

## 📋 工具概述

我为您创建了两个标注检查工具，用于全面验证和可视化 FGSat 演示数据集的标注质量：

### 🔍 1. annotation_checker.py - 全功能检查工具
- **全面验证**：检查所有图像-标注对的完整性和正确性
- **批量可视化**：分批展示标注结果供手动检查
- **交互式导航**：支持前进、后退、跳转等操作
- **统计分析**：提供详细的标注统计信息
- **类别过滤**：可按特定卫星类别过滤显示

### ⚡ 2. quick_annotation_check.py - 快速检查工具
- **快速验证**：轻量级的标注完整性检查
- **随机采样**：随机显示样本进行快速视觉检查
- **简洁报告**：提供简明的错误统计

## 🚀 使用方法

### 方法一：全功能检查（推荐）

```bash
python annotation_checker.py
```

**功能特点：**
- 自动检查所有图像和标注文件
- 详细的错误报告和统计信息
- 交互式批量可视化界面

**交互命令：**
- `ENTER` 或 `n` - 下一批
- `p` - 上一批  
- `g` - 跳转到指定批次
- `s` - 显示统计信息
- `f` - 按类别过滤
- `r` - 重置过滤器
- `q` - 退出

### 方法二：快速检查

```bash
python quick_annotation_check.py
```

**适用场景：**
- 快速验证数据集完整性
- 随机抽样检查标注质量
- 获取基本统计信息

## 📊 检查内容

### 自动验证项目

1. **文件完整性**
   - ✅ 图像文件是否存在
   - ✅ 标注文件是否存在
   - ✅ 文件是否可正常读取

2. **标注格式验证**
   - ✅ YOLO 格式正确性（5个数值）
   - ✅ 类别ID有效性
   - ✅ 坐标范围验证（0-1之间）
   - ✅ 边界框完整性

3. **数据质量检查**
   - ✅ 空标注文件检测
   - ✅ 坐标越界检测
   - ✅ 格式错误检测

### 可视化检查

1. **边界框显示**
   - 🎨 不同颜色区分不同卫星类别
   - 🏷️ 显示类别名称标签
   - 📐 准确的边界框位置

2. **批量展示**
   - 📦 可配置批次大小（1-12张）
   - 🔄 支持前后导航
   - 🎯 显示每张图像的对象数量

## 📈 输出报告

### 验证摘要示例
```
📊 Validation Summary:
============================================================
📁 Total files processed: 600
✅ Valid image-label pairs: 595
❌ Missing labels: 2
❌ Missing/corrupted images: 1
❌ Invalid label files: 2
⚠️  Empty label files: 0

🎯 Validation Success Rate: 99.2%
```

### 统计信息示例
```
📊 Annotation Statistics:
========================================
📁 Total images: 595
📦 Images with objects: 595
🎯 Total objects: 595
📊 Average objects per image: 1.00

🏷️  Objects per class:
   Aquarius: 20 (3.4%)
   Terra: 20 (3.4%)
   XMM-Newton: 20 (3.4%)
   ...
```

## 🔧 配置选项

### 批次大小设置
- **小批次（1-3张）**：适合详细检查
- **中批次（6张）**：默认设置，平衡效率和细节
- **大批次（9-12张）**：适合快速浏览

### 过滤选项
- **按类别过滤**：只显示特定卫星的标注
- **重置过滤**：返回显示所有类别

## 🎯 使用建议

### 首次检查流程
1. **运行全功能检查**：`python annotation_checker.py`
2. **查看验证摘要**：关注错误统计
3. **修复发现的问题**：处理缺失或损坏的文件
4. **进行可视化检查**：批量检查标注准确性
5. **重点检查**：对有问题的类别进行过滤检查

### 日常维护流程
1. **快速检查**：`python quick_annotation_check.py`
2. **随机抽样**：查看几个样本确认质量
3. **问题定位**：如发现问题，使用全功能工具详细检查

## ⚠️ 常见问题处理

### 1. 缺失文件
- **现象**：Missing labels/images 错误
- **解决**：检查文件路径，确保图像和标注文件名匹配

### 2. 格式错误
- **现象**：Invalid label files 错误
- **解决**：检查YOLO格式，确保每行5个数值

### 3. 坐标越界
- **现象**：Coordinates out of range 错误
- **解决**：检查标注工具，确保坐标已正确归一化

### 4. 类别错误
- **现象**：Invalid class ID 错误
- **解决**：检查 dataset_info.json 中的类别映射

## 🎉 预期结果

使用这些工具后，您应该能够：
- ✅ 确认数据集的完整性和正确性
- ✅ 识别并修复标注问题
- ✅ 获得详细的数据集统计信息
- ✅ 通过可视化验证标注质量
- ✅ 为机器学习训练准备高质量数据

## 📞 技术支持

如果在使用过程中遇到问题：
1. 检查文件路径是否正确
2. 确认Python环境包含所需依赖
3. 查看控制台错误信息
4. 根据错误提示进行相应处理

祝您使用愉快！🚀
