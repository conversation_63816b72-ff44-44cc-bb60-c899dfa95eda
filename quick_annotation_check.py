#!/usr/bin/env python3
"""
Quick Annotation Checker for FGSat Demo Dataset

A lightweight tool for rapid annotation validation and basic visualization.
"""

import os
import json
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import matplotlib.image as mpimg
from pathlib import Path
from collections import defaultdict

def load_dataset_info(dataset_path):
    """Load dataset information"""
    info_path = Path(dataset_path) / "dataset_info.json"
    if info_path.exists():
        with open(info_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
            if 'classes' in data:
                classes = {}
                for k, v in data['classes'].items():
                    try:
                        classes[int(k)] = v
                    except ValueError:
                        classes[k] = v
                data['classes'] = classes
            return data
    return {}

def quick_validate_file(image_path, label_path, class_names):
    """Quick validation of a single image-label pair"""
    errors = []
    
    # Check if files exist
    if not image_path.exists():
        return ['missing_image'], []
    if not label_path.exists():
        return ['missing_label'], []
    
    # Try to load image
    try:
        image = mpimg.imread(str(image_path))
        if image is None:
            return ['corrupted_image'], []
    except:
        return ['corrupted_image'], []
    
    # Try to load and parse label
    try:
        with open(label_path, 'r', encoding='utf-8') as f:
            content = f.read().strip()
        
        if not content:
            return ['empty_label'], []
        
        bboxes = []
        for line_num, line in enumerate(content.split('\n'), 1):
            line = line.strip()
            if not line:
                continue
            
            parts = line.split()
            if len(parts) != 5:
                errors.append(f"Line {line_num}: Wrong format")
                continue
            
            try:
                class_id = int(parts[0])
                coords = list(map(float, parts[1:]))
                
                # Basic validation
                if class_id not in class_names:
                    errors.append(f"Line {line_num}: Invalid class {class_id}")
                
                if not all(0 <= c <= 1 for c in coords):
                    errors.append(f"Line {line_num}: Coordinates out of range")
                
                bboxes.append({
                    'class_id': class_id,
                    'coords': coords
                })
                
            except ValueError:
                errors.append(f"Line {line_num}: Invalid numbers")
        
        return errors, bboxes
        
    except Exception as e:
        return [f'file_error: {e}'], []

def quick_check_dataset(dataset_path):
    """Perform quick check on entire dataset"""
    dataset_path = Path(dataset_path)
    dataset_info = load_dataset_info(dataset_path)
    class_names = dataset_info.get('classes', {})
    
    print("🚀 Quick Annotation Check")
    print("=" * 40)
    
    results = {
        'total': 0,
        'valid': 0,
        'errors': defaultdict(int),
        'valid_pairs': []
    }
    
    # Check both RGB and Grayscale
    for img_type in ['RGB', 'Grayscale']:
        img_dir = dataset_path / img_type
        label_dir = dataset_path / f"{img_type}_Labels"
        
        if not img_dir.exists() or not label_dir.exists():
            print(f"⚠️  Skipping {img_type}: directories not found")
            continue
        
        print(f"📁 Checking {img_type} images...")
        
        for img_file in img_dir.glob("*.png"):
            label_file = label_dir / (img_file.stem + ".txt")
            results['total'] += 1
            
            errors, bboxes = quick_validate_file(img_file, label_file, class_names)
            
            if errors:
                for error in errors:
                    results['errors'][error.split(':')[0]] += 1
            else:
                results['valid'] += 1
                results['valid_pairs'].append({
                    'image_path': img_file,
                    'label_path': label_file,
                    'bboxes': bboxes,
                    'type': img_type
                })
    
    # Print summary
    print(f"\n📊 Quick Check Results:")
    print(f"   Total files: {results['total']}")
    print(f"   Valid pairs: {results['valid']}")
    print(f"   Success rate: {results['valid']/results['total']*100:.1f}%")
    
    if results['errors']:
        print(f"\n❌ Error Summary:")
        for error_type, count in results['errors'].items():
            print(f"   {error_type}: {count}")
    
    return results

def show_random_samples(valid_pairs, num_samples=6):
    """Show random samples for quick visual inspection"""
    if not valid_pairs:
        print("❌ No valid pairs to show")
        return
    
    # Select random samples
    import random
    samples = random.sample(valid_pairs, min(num_samples, len(valid_pairs)))
    
    # Create visualization
    cols = min(3, len(samples))
    rows = (len(samples) + cols - 1) // cols
    
    fig, axes = plt.subplots(rows, cols, figsize=(15, 5 * rows))
    if rows == 1 and cols == 1:
        axes = [axes]
    elif rows == 1:
        axes = axes
    else:
        axes = axes.flatten()
    
    fig.suptitle(f'Random Sample Inspection ({len(samples)} images)', 
                fontsize=16, fontweight='bold')
    
    # Generate colors for classes
    dataset_info = load_dataset_info(Path(valid_pairs[0]['image_path']).parent.parent)
    class_names = dataset_info.get('classes', {})
    colors = plt.cm.tab10(np.linspace(0, 1, len(class_names)))
    
    for i, pair in enumerate(samples):
        ax = axes[i]
        
        # Load and display image
        image = mpimg.imread(str(pair['image_path']))
        
        # Handle image format
        if image.dtype != np.uint8:
            image = (image * 255).astype(np.uint8)
        if len(image.shape) == 3 and image.shape[2] == 4:
            image = image[:, :, :3]
        
        ax.imshow(image)
        
        # Draw bounding boxes
        img_height, img_width = image.shape[:2]
        
        for bbox in pair['bboxes']:
            class_id = bbox['class_id']
            coords = bbox['coords']
            
            # Convert normalized to pixel coordinates
            x_center, y_center, width, height = coords
            x_center *= img_width
            y_center *= img_height
            width *= img_width
            height *= img_height
            
            # Rectangle coordinates
            x = x_center - width / 2
            y = y_center - height / 2
            
            # Choose color
            color = colors[class_id % len(colors)]
            
            # Draw box
            rect = patches.Rectangle(
                (x, y), width, height,
                linewidth=2, edgecolor=color, facecolor='none'
            )
            ax.add_patch(rect)
            
            # Add label
            class_name = class_names.get(class_id, f"C{class_id}")
            ax.text(x, y - 5, class_name, 
                   bbox=dict(boxstyle="round,pad=0.3", facecolor=color, alpha=0.7),
                   fontsize=8, color='white', fontweight='bold')
        
        # Set title
        filename = pair['image_path'].name
        ax.set_title(f'{filename}\n{pair["type"]} - {len(pair["bboxes"])} objects', 
                    fontsize=10)
        ax.axis('off')
    
    # Hide unused subplots
    for i in range(len(samples), len(axes)):
        axes[i].axis('off')
    
    plt.tight_layout()
    plt.show()

def main():
    """Main function"""
    dataset_path = r"G:\ICCSSE\数据集\FGSat_Demo"
    
    # Run quick check
    results = quick_check_dataset(dataset_path)
    
    if results['valid'] == 0:
        print("❌ No valid pairs found!")
        return
    
    # Ask if user wants to see samples
    while True:
        choice = input(f"\n🎨 Show random samples? (y/n): ").strip().lower()
        if choice in ['y', 'yes']:
            try:
                num_samples = input("How many samples? (default 6): ").strip()
                num_samples = int(num_samples) if num_samples else 6
                num_samples = max(1, min(num_samples, 12))
            except ValueError:
                num_samples = 6
            
            show_random_samples(results['valid_pairs'], num_samples)
            break
        elif choice in ['n', 'no']:
            break
        else:
            print("Please enter 'y' or 'n'")
    
    print("\n✅ Quick check complete!")

if __name__ == "__main__":
    main()
