# FGSat: Fine-Grained Satellite Classification Dataset

## 🚀 About FGSat Dataset

**FGSat** is the first large-scale benchmark dataset specifically designed for **fine-grained satellite classification (FGSC)**, addressing the critical need for distinguishing between visually similar satellite models in orbital operations such as in-orbit servicing, formation flying, and active debris removal.

### Key Features
- **Fine-Grained Focus**: Captures subtle structural differences between similar satellites
- **Comprehensive Coverage**: 30 satellite types spanning Earth observation and scientific missions
- **Large Scale**: 60,000 high-fidelity synthetic images (30K RGB + 30K Grayscale)
- **Automatic Annotation**: Precise bounding box annotations generated through simulation
- **Realistic Rendering**: Unity Engine with Physically-Based Rendering (PBR) technology
- **Diverse Conditions**: Multiple imaging scenarios including normal, overexposed, out-of-focus, and noisy conditions

## 🛰️ Satellite Categories

The FGSat dataset encompasses **30 distinct satellite models** carefully selected to represent the diversity of modern space missions. These satellites span two major categories:

### 🌍 Earth Observation Satellites (17 types)
Focused on monitoring Earth's climate, weather, land use, and environmental changes:

| Satellite | Primary Function |
|-----------|------------------|
| **Aqua** | Global Water Cycle |
| **Aquarius** | Sea Surface Salinity |
| **Aura** | Atmospheric Chemistry & Ozone |
| **CALIPSO** | Clouds & Aerosols |
| **CloudSat** | Cloud Structure & Water Content |
| **GOES** | Weather & Storms |
| **GPM** | Global Precipitation |
| **ICESat-2** | Ice Sheets & Land Elevation |
| **LandSat8** | Land Cover & Vegetation |
| **OCO2** | Atmospheric CO₂ |
| **POES** | Meteorology & Environment |
| **SAC-C** | Land Use & Atmosphere |
| **SeaStar** | Ocean Color & Phytoplankton |
| **Terra** | Land & Climate Change |
| **TOPEX** | Ocean Topography |
| **TDRS** | Data Relay |
| **Tselina-2** | Electronic Intelligence |

### 🔬 Scientific Research Satellites (13 types)
Dedicated to space science, astronomy, and fundamental physics research:

| Satellite | Primary Function |
|-----------|------------------|
| **Cluster** | Magnetosphere Dynamics |
| **Double-Star** | Magnetosphere & Plasma |
| **FUSE** | Ultraviolet Astronomy |
| **GLAST** | Gamma-ray Astronomy |
| **Hinode** | Solar Magnetic Fields |
| **ICON** | Ionosphere & Atmosphere |
| **MMS** | Earth Magnetic Reconnection |
| **Polar** | Particles & Ionized Gas |
| **Proba-2** | New Spacecraft Technologies Validation |
| **Swift** | Gamma-ray Bursts |
| **SWAS** | Interstellar Water & Molecules |
| **Tango** | Formation Flying Technology Validation |
| **XMM-Newton** | X-ray Astronomy |


### Full Dataset Specifications
- **📈 Scale**: 60,000 high-fidelity images (30K RGB + 30K Grayscale)
- **🎯 Classes**: Same 30 satellite types with 2,000 images per class
- **🌟 Imaging Conditions**:
  - 700 standard images per class
  - 100 overexposed images per class
  - 100 out-of-focus images per class
  - 100 noise-artifact images per class
- **📐 Resolution**: 1920×1080 pixels
- **🔧 Annotations**: Automatic bounding box generation via simulation
- **📊 Data Split**: 70% training, 20% validation, 10% testing

## 📋 Demo Dataset Overview

**This is a demonstration version of the FGSat dataset**, providing a representative sample for rapid prototyping, algorithm testing, and educational purposes. The complete FGSat dataset will be released upon paper acceptance.

### Demo Statistics
- **Total Classes**: 30 satellite types
- **Total Images**: 600 (1% of full dataset)
- **RGB Images**: 300
- **Grayscale Images**: 300
- **Images per Class**: 20 (10 RGB + 10 Grayscale)
- **Image Resolution**: 1920×1080 pixels
- **Image Format**: PNG
- **Sampling Strategy**: Uniform interval sampling from standard imaging conditions

## Directory Structure
```
FGSat_Demo/
├── RGB/                    # 300 RGB images
├── RGB_Labels/             # 300 YOLO format label files for RGB
├── Grayscale/             # 300 Grayscale images  
├── Grayscale_Labels/      # 300 YOLO format label files for Grayscale
├── dataset_info.json     # Detailed dataset information
└── README.md            # This file
```


## 📜 License

The FGSat dataset will be released under an open-source license to facilitate research and development in the space domain. Specific license terms will be announced with the full release.
