# FGSat Demo Dataset

## Overview
This is a demonstration version of the FGSat dataset for fine-grained satellite classification research. The dataset is specifically designed for testing and showcasing satellite recognition algorithms.

## Dataset Statistics
- **Total Classes**: 30 satellite types
- **Total Images**: 600
- **RGB Images**: 300
- **Grayscale Images**: 300
- **Images per Class**: 20 (10 RGB + 10 Grayscale)
- **Image Resolution**: 1920×1080 pixels
- **Image Format**: PNG

## Directory Structure
```
FGSat_Demo/
├── RGB/                    # 300 RGB images
├── RGB_Labels/             # 300 YOLO format label files for RGB
├── Grayscale/             # 300 Grayscale images  
├── Grayscale_Labels/      # 300 YOLO format label files for Grayscale
├── dataset_info.json     # Detailed dataset information
└── README.md            # This file
```

## Satellite Classes
The demo includes the following 30 satellite types from various space missions:

 1. **Aquaruis**
 2. **OCO2**
 3. **Tango**
 4. **POES**
 5. **SAC-C**
 6. **SeaStar**
 7. **Terra**
 8. **TOPEX**
 9. **TDRS**
10. **Tselina-2**
11. **Double-Star**
12. **Aqua**
13. **Cluster**
14. **Proba-2**
15. **XMM-Newton**
16. **FUSE**
17. **GLAST**
18. **Hinode**
19. **ICON**
20. **MMS**
21. **Swift**
22. **Polar**
23. **LandSat8**
24. **SWAS**
25. **Aura**
26. **CALIPSO**
27. **CloudSat**
28. **GOES**
29. **GPM**
30. **ICESat-2**

## File Naming Convention
Images are systematically named using the format: `SpacecraftName_ImageType_Index.png`

**Examples:**
- `Aquarius_rgb_001.png` - First RGB image of Aquarius satellite
- `Aquarius_rgb_001.txt` - Corresponding YOLO label file
- `Terra_grayscale_005.png` - Fifth grayscale image of Terra satellite  
- `Terra_grayscale_005.txt` - Corresponding YOLO label file
- `ICESat-2_rgb_008.png` - Eighth RGB image of ICESat-2 satellite
- `ICESat-2_rgb_008.txt` - Corresponding YOLO label file

## YOLO Label Format
Each `.txt` file contains object annotations in YOLO format:
```
class_id center_x center_y width height
```
- `class_id`: Integer (0-29) representing satellite class
- `center_x, center_y`: Normalized center coordinates (0.0-1.0)
- `width, height`: Normalized bounding box dimensions (0.0-1.0)

**Example label file content:**
```
0 0.512 0.423 0.245 0.356
```

## Usage Examples
This demo dataset can be used for:
- **Algorithm Testing**: Quick validation of satellite classification models
- **Educational Purposes**: Teaching fine-grained visual classification
- **Research Demonstrations**: Showcasing satellite recognition capabilities
- **Preliminary Development**: Initial testing before using the full dataset

## Technical Details
- **Sampling Strategy**: Uniform interval sampling (not random) for representative coverage
- **Data Source**: Only standard folders (1-RGB-700, 2-灰度-700) - excludes augmented data
- **Quality Control**: Only camera images are included (excludes metadata files)
- **Interval Calculation**: For 10 samples from 700 images, selects every ~70th image
- **Grayscale Conversion**: RGB images are converted to grayscale when native grayscale images are unavailable

## Loading the Dataset
### Python Example
```python
import os
from pathlib import Path

# Load RGB images
rgb_dir = Path("FGSat_Demo/RGB")
rgb_images = list(rgb_dir.glob("*.png"))
print(f"Found {len(rgb_images)} RGB images")

# Load Grayscale images  
gray_dir = Path("FGSat_Demo/Grayscale")
gray_images = list(gray_dir.glob("*.png"))
print(f"Found {len(gray_images)} Grayscale images")

# Extract class names from filenames
classes = set([img.stem.split('_')[0] for img in rgb_images])
print(f"Found {len(classes)} satellite classes")
```

## Dataset Characteristics
- **Fine-Grained Classification**: Focuses on distinguishing between similar satellite types
- **Multi-Modal**: Includes both RGB and grayscale imagery
- **Realistic Rendering**: High-fidelity synthetic images with realistic lighting and backgrounds
- **Diverse Viewpoints**: Multiple camera angles and distances for each satellite
- **Mission Variety**: Covers Earth observation, scientific, and communication satellites

## Original Dataset
This demo is derived from the comprehensive FGSat dataset, which contains extensive satellite imagery for advanced fine-grained classification research. The full dataset includes:
- Multiple image conditions (normal, overexposed, blurred, noisy)
- Temporal sequences showing satellite motion
- Detailed metadata including camera parameters and satellite poses

## Citation
If you use this dataset in your research, please cite our paper:
```bibtex
@inproceedings{fgsat2024,
  title={FGSat: A New Dataset and Performance Benchmark for Fine-Grained Satellite Classification},
  author={[Authors]},
  booktitle={[Conference]},
  year={2024}
}
```

## Contact Information
For questions, issues, or collaboration opportunities, please contact:
- **Email**: [Contact Email]
- **GitHub**: [Repository URL]
- **Paper**: [Paper URL when available]

## License
[License information to be added]

---
**Generated Information:**
- Created: 600 images from 30 satellite classes
- Sampling: Uniform intervals (deterministic, reproducible)
- Data Source: Standard folders only (excludes augmented data)
- Generated by: FGSat Demo Dataset Creator v1.0
- Creation Date: 2024

*This demo dataset provides a representative sample of the full FGSat dataset for rapid prototyping and evaluation.*
